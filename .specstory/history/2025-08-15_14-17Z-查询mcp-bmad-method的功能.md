<!-- Generated by SpecStory -->

# 查询MCP BMAD-METHOD的功能 (2025-08-15 14:17Z)

_**User**_

调用MCP BMAD-METHOD ，查询下都实现了什么功能

---

_**Assistant**_

<think><details><summary>Thought Process</summary>**Planning document search**

I'm thinking about how to approach this. I'll start by fetching the document and then proceed to search for terms like "功能", "features", "支持", "能力", and "BMAD METHOD 是什么". 

I realize that while the tools can handle parallel searches, the search hinges on successfully fetching the document first. It makes sense to first ensure that the content is loaded, even though I need to keep the default of parallel searches in mind. So, fetching comes first, then multiple parallel searches afterwards with queries relevant to features and overviews.
**Organizing tool calls and feedback**

I need to start with the documentation and provide a status update before calling the tool, keeping it concise. Once I have the content, I'll summarize the functionalities in high-level bullet points. I’ll use markdown rules like adding headings, bolding key items, and using backticks for code or file names.

After that, I should call the interactive feedback tool to confirm with the user, ensuring we keep checking for their feedback until they have none. I'll also include a prompt asking if they want a brief overview or a detailed feature list. Plus, I should remember to cite the memory about using pytest as the default test runner.
**Preparing to fetch features**

I realize it’s irrelevant to cite the memory here since it doesn’t apply to this context. The memory system requires mentioning it only when it's relevant. So, I’ll just focus on presenting the list of features without heavy formatting, but I can still use headings and bullet points. Now, it’s time to call the fetch tool to gather the necessary content. Once I have that, I can move forward with the feature list.</details></think>

---

我先读取 BMAD-METHOD 的完整文档，以便汇总其提供的功能范围与能力清单。随后我会给出简明要点，并确认你是否需要更细项的模块级功能说明或使用示例。

---

<details>
          <summary>MCP tool call</summary>
      
*Called tool*: BMAD-METHOD_Docs - fetch_BMAD_METHOD_documentation: Fetch entire documentation file from GitHub repository: bmad-code-org/BMAD-METHOD. Useful for general questions. Always call this tool first if asked about bmad-code-org/BMAD-METHOD.

*Parameters*:
  * random_string: init

*Result*:
# bmad-code-org/BMAD-METHOD/docs Documentation
Below is a structured llms.txt index of Markdown files extracted from the /docs folder. Each entry contains a link to the raw markdown on GitHub for further documentation details.


## Root Files



- [GUIDING-PRINCIPLES.md](https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/GUIDING-PRINCIPLES.md): BMad Method Guiding Principles for AI-Assisted Software Development
The BMad Method is a natural language framework for AI-assisted software development, emphasizing lean development agents, natural language instructions, and structured templates. Core principles include minimizing dev agent dependencies, using natural language for agents and tasks, and designing templates with YAML format. Practical guidelines cover adding to the core, creating expansion packs, agent design, task writing, and template rules. The framework separates processing logic from generated content, ensuring clean output. Key concepts include dev agents, planning agents, templates, tasks, and expansion packs.

```yml
# Example of template structure
template:
  id: example-template
  name: Example Template
  version: 1.0
  output:
    format: markdown
workflow:
  interactionMode: interactive
sections:
  - id: introduction
    title: Introduction
    instruction: Write a brief introduction
```

Note that there are additional code examples mentioned in the document, such as variable substitution using `{{variable_name}}` syntax and conditional sections with `condition` field.

KEY TERMS:

1. BMad Method
2. Dev Agents
3. Planning Agents
4. Natural Language Framework
5. YAML Templates
6. Expansion Packs
7. Agent Design
8. Task Writing
9. Template Rules
10. AI-Assisted Software Development


- [core-architecture.md](https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/core-architecture.md): BMad Method Core Architecture Overview
The BMad Method is a structured framework designed to provide agentic modes, tasks, and templates for repeatable and helpful workflows. It facilitates a full development lifecycle tailored to modern AI agentic tooling, covering ideation, planning, architecture, design, and development execution. The core module, `bmad-core`, acts as the brain of the operation, with the `tools` directory providing means to process and package it for different environments. The framework includes agents, agent teams, workflows, templates, tasks, checklists, and data, which are dynamically loaded based on agent dependencies. A key architectural principle is that templates are self-contained and interactive, embedding both desired document output and LLM instructions.

```markdown
Example of template processing system components:
- `template-format.md`: Defines foundational markup language for BMad templates.
- `create-doc.md`: Manages document generation workflow.

Example of technical preferences system:
The `technical-preferences.md` file serves as a persistent technical profile influencing agent behavior across projects.
```

Additional code examples include Mermaid diagrams for system architecture and workflow illustrations.

KEY TERMS:
1. BMad Method
2. Agentic modes
3. Workflows
4. Agent teams
5. Templates
6. Technical preferences
7. BMad-core
8. Web-builder.js
9. Mermaid diagrams
10. LLM instructions


- [enhanced-ide-development-workflow.md](https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/enhanced-ide-development-workflow.md): Enhanced Development Workflow with BMad Method
This document outlines a step-by-step guide to efficiently manage the development workflow using the BMad Method. It covers the creation of a new branch, story creation, implementation, review, and commit changes. The guide is divided into sections for Scrum Master, Developer, and Quality Assurance roles. Key technical terms include BMad Method, story creation, and agent execution. The document provides commands for executing tasks, such as `*draft` for creating a new story and `*develop-story {selected-story}` for implementing a story.

Example commands:
```markdown
*draft (runs create-next-story task)
*develop-story {selected-story} (runs execute-checklist task)
```
The document also mentions other commands, such as `*review {selected-story}` for reviewing a story.

KEY TERMS:
1. BMad Method
2. Story Creation
3. Scrum Master
4. Developer
5. Quality Assurance
6. Branch Creation
7. Agent Execution
8. Checklist Task
9. Story Implementation
10. Story Review


- [expansion-packs.md](https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/expansion-packs.md): BMad Expansion Packs: Unlocking AI Potential Across Domains
The BMad Method's expansion packs extend the framework's natural language AI orchestration to various domains, transforming it into a universal AI agent system. The core framework focuses on software development, while expansion packs handle other areas. There are technical packs (Game Development, Mobile Development, DevOps, Data Science) and non-technical packs (Business Strategy, Creative Writing, Health & Wellness, Education). Each pack provides specialized knowledge and can be installed as needed. Users can create their own packs by defining a domain, designing agents, creating tasks, building templates, testing, and sharing. The future of expansion packs includes a marketplace potential, AI agent ecosystems, and democratizing expertise. To get started, users can browse existing packs, install what they need, create their own, and share with others.

No code examples are provided in the document.

KEY TERMS:
1. BMad Method
2. Expansion Packs
3. AI Orchestration
4. Natural Language AI
5. Game Development Pack
6. Mobile Development Pack
7. DevOps Pack
8. Data Science Pack
9. Business Strategy Pack
10. AI Agent Ecosystems


- [how-to-contribute-with-pull-requests.md](https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/how-to-contribute-with-pull-requests.md): Contributing to BMAD-METHOD with Pull Requests Guide
This guide provides a step-by-step walkthrough on how to contribute to the BMAD-METHOD repository using pull requests. It covers the basics of creating a pull request, including forking the repository, creating a new branch, making changes, committing, pushing, and creating a pull request. The guide emphasizes the importance of keeping contributions small and focused, and provides tips on writing good commit messages and creating effective pull requests. Key technical terms include "fork," "branch," "commit," and "pull request." The guide also includes code examples for cloning a repository and creating a new branch.

```bash
# Clone the repository
git clone https://github.com/YOUR-USERNAME/bmad-method.git
cd bmad-method

# Create and switch to a new branch
git checkout -b fix/typo-in-readme
```

The document also includes additional code examples for committing changes and pushing to a fork.

KEY TERMS:
1. Pull Request
2. Fork
3. Branch
4. Commit
5. GitHub
6. Repository
7. Discord
8. Issue
9. Feature Request
10. Bug Report


- [user-guide.md](https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/user-guide.md): BMad-Method User Guide for Agile AI-Driven Planning
The BMad-Method is a guide for agile AI-driven planning and development. It involves a structured planning workflow, which includes creating a project brief, product requirements document (PRD), and architecture. The method uses powerful web agents and IDE agents to facilitate the process. The guide covers the installation of the BMad Method, working in greenfield and brownfield projects, and using special agents like BMad-Master and BMad-Orchestrator. It also provides best practices for IDE integration, technical preferences, and core configuration.

The planning workflow involves several steps:
```mermaid
graph TD
    A["Start: Project Idea"] --> B{"Optional: Analyst Research"}
    B -->|Yes| C["Analyst: Brainstorming (Optional)"]
    B -->|No| G{"Project Brief Available?"}
    C --> C2["Analyst: Market Research (Optional)"]
    C2 --> C3["Analyst: Competitor Analysis (Optional)"]
    C3 --> D["Analyst: Create Project Brief"]
    D --> G
    G -->|Yes| E["PM: Create PRD from Brief (Fast Track)"]
    G -->|No| E2["PM: Interactive PRD Creation (More Questions)"]
    E --> F["PRD Created with FRs, NFRs, Epics & Stories"]
    E2 --> F
    F --> F2{"UX Required?"}
    F2 -->|Yes| F3["UX Expert: Create Front End Spec"]
    F2 -->|No| H["Architect: Create Architecture from PRD"]
    F3 --> F4["UX Expert: Generate UI Prompt for Lovable/V0 (Optional)"]
    F4 --> H2["Architect: Create Architecture from PRD + UX Spec"]
    H --> I["PO: Run Master Checklist"]
    H2 --> I
    I --> J{"Documents Aligned?"}
    J -->|Yes| K["Planning Complete"]
    J -->|No| L["PO: Update Epics & Stories"]
    L --> M["Update PRD/Architecture as needed"]
    M --> I
    K --> N["📁 Switch to IDE (If in a Web Agent Platform)"]
    N --> O["PO: Shard Documents"]
    O --> P["Ready for SM/Dev Cycle"]
```

Example code for interacting with agents in the IDE:
```bash
# Some Ide's, like Cursor or Windsurf for example, utilize manual rules so interaction is done with the '@' symbol
@pm Create a PRD for a task management app
@architect Design the system architecture
@dev Implement the user authentication
```

The document also includes additional code examples, such as YAML sections for defining agent dependencies and configuration files for core configuration.

KEY TERMS:
1. BMad-Method
2. Agile AI-driven planning
3. Web agents
4. IDE agents
5. BMad-Master
6. BMad-Orchestrator
7. Technical preferences
8. Core configuration
9. Project brief
10. Product requirements document (PRD)


- [versioning-and-releases.md](https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/versioning-and-releases.md): Releasing New Versions of BMAD-METHOD
This document outlines the process for releasing new versions of BMAD-METHOD, a project that utilizes automatic semantic releases. The recommended approach involves committing with specific message formats (e.g., `fix:`, `feat:`, `feat!:`) to trigger patch, minor, or major releases. GitHub Actions automate the release process, including version bumping, changelog generation, and publishing to NPM. For exceptions, manual release methods are provided, such as running `npm run version:patch`, `npm run version:minor`, or `npm run version:major`. The document also explains how to test the setup with `npm run release:test`.

Example commit messages:
```bash
fix: resolve CLI argument parsing bug
feat: add new agent orchestration mode
```
Additional examples include manual version bumps and GitHub Actions workflow dispatch.

KEY TERMS:
1. Automatic semantic releases
2. Commit message format
3. GitHub Actions
4. NPM publishing
5. Semantic versioning
6. Release automation
7. Changelog generation
8. Version bumping
9. Manual release methods
10. GitHub release notes


- [versions.md](https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/versions.md): BMAD Method Version History Documentation
This document provides a version history of the BMAD Method, a system designed to streamline project planning and development using custom agents with agile personas. The document outlines the guiding principles, features, and improvements of each version, from V1 to V4. The BMAD Method aims to provide a simple, yet powerful tool for project management, with a focus on simplicity, configurability, and ease of use. Key concepts include the use of agents, personas, and templates to facilitate project planning and development.

The document highlights the major changes and improvements in each version, including the introduction of new features, such as customizability, separation of concerns, and web versions. For example, V3 introduced a full explosion of customizability, including tasks, personas, agent configurations, and doc templates. However, this also introduced complexity, which was addressed in later versions.

No code examples are provided in the document.

KEY TERMS:

* BMAD Method
* Version History
* Custom Agents
* Agile Personas
* Project Planning
* Development
* Configurability
* Separation of Concerns
* Web Versions
* Gemini Gems

Note that there are no code examples in the document, so none are provided. The document is primarily a narrative description of the BMAD Method's version history and features.


- [working-in-the-brownfield.md](https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/working-in-the-brownfield.md): Working in the Brownfield: A Complete Guide
This comprehensive guide covers brownfield development using BMad-Method, focusing on adding features, fixing bugs, or modernizing existing software projects. It outlines the complete brownfield workflow, including choosing between PRD-First and Document-First approaches, defining requirements, focused documentation, planning enhancements, validating planning artifacts, and transitioning to development. The guide also provides best practices, common scenarios, troubleshooting tips, and a quick reference section.

Example code:
```bash
@pm
*create-brownfield-prd
```
```bash
@architect
*document-project
```
The document also includes additional code examples for various commands and workflows, such as `create-brownfield-epic`, `create-brownfield-story`, and `shard` commands.

KEY TERMS:

1. Brownfield development
2. BMad-Method
3. PRD-First approach
4. Document-First approach
5. Brownfield PRD
6. Architecture document
7. Feature flags
8. Integration points
9. Regression testing
10. Backwards compatibility



</details>

---



---

我先并行检索 BMAD-METHOD 文档中的“功能/能力、工作流、智能体、模板、扩展包”等核心条目，以便给你一份精炼的功能清单。随后我会继续补充命令与IDE集成功能。

---

<details>
          <summary>MCP tool call</summary>
      
*Called tool*: BMAD-METHOD_Docs - search_BMAD_METHOD_documentation: Semantically search within the fetched documentation from GitHub repository: bmad-code-org/BMAD-METHOD. Useful for specific queries.

*Parameters*:
  * query: What features or capabilities does BMad Method provide?

*Result*:
## Query

What features or capabilities does BMad Method provide?.

## Response

### Sources:
Important: you can fetch the full content of any source using the fetch_url_content tool

#### (bmad-code-org/BMAD-METHOD/llms.txt)[https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/llms.txt] (Score: 0.59)
- 

Example of BMad workflow:
graph TD
    A["Start: Project Idea"] --> B{"Optional: Analyst Brainstorming"}
    B -->|Yes| C["Analyst: Market Research & Analysis"]
```

Note that there are additional code examples and diagrams throughout the document, including Mermaid diagrams for system architecture and workflows.

KEY TERMS:
1. BMad Method
2. Agentic modes
3. Template processing system
4. Technical preferences system
5. Agent teams
6. Workflows
7. BMad-core
8. Web-builder.js
9. Development lifecycle
10. AI agentic tooling


- [enhanced-ide-development-workflow.md](https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/enhanced-ide-development-workflow.md): Enhanced Development Workflow with BMad Method
This document outlines a step-by-step guide to efficiently manage the development workflow using the BMad Method. It covers creating a new branch, story creation, implementation, review, and committing changes. The guide is divided into sections for Scrum Master, Developer, and Quality Assurance roles. Key technical terms include BMad Method, story creation, and agent execution. The document provides commands for executing tasks, such as `*draft` for creating a new story and `*develop-story {selected-story}` for implementing a story. 

Example commands:
```markdown
*draft (runs create-next-story task)
*develop-story {selected-story} (runs execute-checklist task)
```
The document also mentions additional commands, such as `*review {selected-story}` for reviewing a story.

KEY TERMS:
1. BMad Method
2. Story Creation
3. Scrum Master
4. Developer
5. Quality Assurance
6. Branch Creation
7. Agent Execution
8. Checklist Task
9. Story Implementation
10. Story Review

- 

KEY TERMS:
1. BMad Method
2. Story Creation
3. Scrum Master
4. Developer
5. Quality Assurance
6. Branch Creation
7. Agent Execution
8. Checklist Task
9. Story Implementation
10. Story Review


- [expansion-packs.md](https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/expansion-packs.md): BMad Expansion Packs: Unlocking AI Potential Across Domains
The BMad Method's expansion packs extend the framework's natural language AI orchestration to various domains, transforming it into a universal AI agent system. The core framework focuses on software development, while expansion packs handle other areas. There are technical packs (Game Development, Mobile Development, DevOps, Data Science) and non-technical packs (Business Strategy, Creative Writing, Health & Wellness, Education). Each pack provides specialized knowledge and can be installed as needed. Users can create their own packs by defining a domain, designing agents, creating tasks, building templates, testing, and sharing. The future of expansion packs includes a marketplace, AI agent ecosystems, and democratizing expertise.

No code examples are provided in the document.

KEY TERMS:
1. BMad Method
2. Expansion Packs
3. AI Orchestration
4. Natural Language AI
5. Game Development Pack
6. Mobile Development Pack
7. DevOps Pack
8. Data Science Pack
9. Business Strategy Pack
10. AI Agent Ecosystems

- 
# Example of template structure
template:
  id: example-template
  name: Example Template
  version: 1.0
  output:
    format: markdown
workflow:
  interactionMode: guided
sections:
  - id: introduction
    title: Introduction
    instruction: Write a brief introduction
```

The document also mentions the use of variable substitution, conditional sections, and repeatable sections in templates.

Some additional code examples are provided in the document, including YAML templates with structured sections and metadata.

KEY TERMS:

1. BMad Method
2. Natural Language Framework
3. AI-Assisted Software Development
4. Dev Agents
5. Planning Agents
6. Templates
7. Expansion Packs
8. YAML Format
9. Agent Design
10. Task Writing


- [core-architecture.md](https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/core-architecture.md): BMad Method Core Architecture Overview
The BMad Method is a structured framework designed to facilitate agentic modes, tasks, and templates for repeatable and helpful workflows. It provides a full development lifecycle tailored to modern AI agentic tooling, covering ideation, planning, architecture, design, and development execution. The core module, `bmad-core`, acts as the brain of the operation and is supported by the `tools` directory for processing and packaging. The framework includes agents, agent teams, workflows, templates, tasks, checklists, and data, which are dynamically loaded based on agent dependencies. A key feature is the template processing system, which allows for self-contained and interactive templates. The framework also includes a technical preferences system for personalized agent behavior.

```markdown
Example of template processing system components:
- `template-format.md`: Defines the foundational markup language for BMad templates.
- `create-doc.md`: Manages the document generation workflow.

- 


- [versions.md](https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/versions.md): BMAD Method Version History Documentation
This document provides a version history of the BMAD Method, a system designed to streamline project planning and development using custom agents with agile personas. The document outlines the guiding principles, features, and improvements of each version, from V1 to V4. The BMAD Method aims to provide a simple, yet powerful tool for project management, with a focus on simplicity, configurability, and ease of use. Key concepts include the use of agents, personas, and templates to facilitate project planning and development.

The document highlights the major changes and improvements in each version, including the introduction of new features, such as customizability, separation of concerns, and web versions. For example, V3 introduced a full explosion of customizability, including tasks, personas, agent configurations, and doc templates. However, this also introduced complexity, which was addressed in later versions.

No code examples are provided in the document.

KEY TERMS:

* BMAD Method
* Version History
* Custom Agents
* Agile Personas
* Project Planning
* Development
* Configurability
* Separation of Concerns
* Web Versions
* Gemini Gems

Note that there are no code examples in the document, so none are provided. The document is primarily a narrative description of the BMAD Method's version history and features.

- # bmad-code-org/BMAD-METHOD/docs Documentation
Below is a structured llms.txt index of Markdown files extracted from the /docs folder. Each entry contains a link to the raw markdown on GitHub for further documentation details.


## Root Files



- [GUIDING-PRINCIPLES.md](https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/GUIDING-PRINCIPLES.md): BMad Method Guiding Principles for AI-Assisted Software Development
The BMad Method is a natural language framework for AI-assisted software development, emphasizing lean development agents, natural language instructions, and structured templates. Core principles include minimizing dev agent dependencies, using natural language for agents and tasks, and designing templates with YAML format. Practical guidelines cover adding to the core, creating expansion packs, agent design, task writing, and template rules. The framework separates processing logic from generated content, ensuring clean output. Key concepts include dev agents, planning agents, templates, and expansion packs.

The BMad Method Guiding Principles document outlines the framework's core principles, practical guidelines, and technical specifications. It emphasizes the importance of natural language orchestration, lean dev agents, and structured templates. The document provides detailed information on agent design, task writing, and template rules, ensuring that contributions maintain the method's effectiveness.

```yml
# Example of template structure
template:
  id: example-template
  name: Example Template
  version: 1.0
  output:
    format: markdown
workflow:
  interactionMode: guided
sections:
  - id: introduction
    title: Introduction
    instruction: Write a brief introduction
```

The document also mentions the use of variable substitution, conditional sections, and repeatable sections in templates.

Some additional code examples are provided in the document, including YAML templates with structured sections and metadata.

KEY TERMS:

1. BMad Method
2. Natural Language Framework
3. AI-Assisted Software Development
4. Dev Agents
5. Planning Agents
6. Templates
7. Expansion Packs
8. YAML Format
9. Agent Design
10. Task Writing


- [core-architecture.md](https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/core-architecture.md): BMad Method Core Architecture Overview
The BMad Method is a structured framework designed to facilitate agentic modes, tasks, and templates for repeatable and helpful workflows. It provides a full development lifecycle tailored to modern AI agentic tooling, covering ideation, planning, architecture, design, and development execution. The core module, `bmad-core`, acts as the brain of the operation and is supported by the `tools` directory for processing and packaging. The framework includes agents, agent teams, workflows, templates, tasks, checklists, and data, which are dynamically loaded based on agent dependencies. A key feature is the template processing system, which allows for self-contained and interactive templates. The framework also includes a technical preferences system for personalized agent behavior.

```markdown
Example of template processing system components:
- `template-format.md`: Defines the foundational markup language for BMad templates.
- `create-doc.md`: Manages the document generation workflow.

Example of BMad workflow:
graph TD
    A["Start: Project Idea"] --> B{"Optional: Analyst Brainstorming"}
    B -->|Yes| C["Analyst: Market Research & Analysis"]
```

Note that there are additional code examples and diagrams throughout the document, including Mermaid diagrams for system architecture and workflows.

KEY TERMS:
1. BMad Method
2. Agentic modes
3. Template processing system
4. Technical preferences system
5. Agent teams
6. Workflows
7. BMad-core
8. Web-builder.js
9. Development lifecycle
10. AI agentic tooling


- [enhanced-ide-development-workflow.md](https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/enhanced-ide-development-workflow.md): Enhanced Development Workflow with BMad Method
This document outlines a step-by-step guide to efficiently manage the development workflow using the BMad Method. It covers creating a new branch, story creation, implementation, review, and committing changes. The guide is divided into sections for Scrum Master, Developer, and Quality Assurance roles. Key technical terms include BMad Method, story creation, and agent execution. The document provides commands for executing tasks, such as `*draft` for creating a new story and `*develop-story {selected-story}` for implementing a story. 

Example commands:
```markdown
*draft (runs create-next-story task)
*develop-story {selected-story} (runs execute-checklist task)
```
The document also mentions additional commands, such as `*review {selected-story}` for reviewing a story.

KEY TERMS:
1. BMad Method
2. Story Creation
3. Scrum Master
4. Developer
5. Quality Assurance
6. Branch Creation
7. Agent Execution
8. Checkli [CONTENT TRIMMED DUE TO SIZE]
- 

Note that there are no code examples in the document, so none are provided. The document is primarily a narrative description of the BMAD Method's version history and features.


- [working-in-the-brownfield.md](https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/working-in-the-brownfield.md): Working in the Brownfield: A Complete Guide
This comprehensive guide covers Brownfield development using BMad-Method, focusing on adding features, fixing bugs, or modernizing existing software projects. It outlines the Brownfield workflow, including choosing between PRD-First and Document-First approaches, and provides detailed steps for each phase. Key concepts include using Gemini Web or Gemini CLI for documentation generation, creating PRDs, and generating architecture documents. The guide also offers best practices, common scenarios, and troubleshooting tips.

Example code:
```bash


#### (bmad-code-org/BMAD-METHOD/docs/user-guide.md)[https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/user-guide.md] (Score: 0.58)
- # BMad-Method BMAd Code User Guide

This guide will help you understand and effectively use the BMad Method for agile AI driven planning and development.

## The BMad Plan and Execute Workflow

First, here is the full standard Greenfield Planning + Execution Workflow. Brownfield is very similar, but it's suggested to understand this greenfield first, even if on a simple project before tackling a brownfield project. The BMad Method needs to be installed to the root of your new project folder. For the planning phase, you can optionally perform it with powerful web agents, potentially resulting in higher quality results at a fraction of the cost it would take to complete if providing your own API key or credits in some Agentic tools. For planning, powerful thinking models and larger context - along with working as a partner with the agents will net the best results.

If you are going to use the BMad Method with a Brownfield project (an existing project), review **[Working in the Brownfield](./working-in-the-brownfield.md)**.

If you do not see the diagrams that following rendering, you can install Markdown All in One along with the Markdown Preview Mermaid Support plugins to VSCode (or one of the forked clones). With these plugin's, if you right click on the tab when open, there should be a Open Preview option, or check the IDE documentation.

### The Planning Workflow (Web UI or Powerful IDE Agents)

Before development begins, BMad follows a structured planning workflow that's ideally done in web UI for cost efficiency:

```mermaid
graph TD
    A["Start: Project Idea"] --> B{"Optional: Analyst Research"}
    B -->|Yes| C["Analyst: Brainstorming (Optional)"]
    B -->|No| G{"Project Brief Available?"}
    C --> C2["Analyst: Market Research (Optional)"]
    C2 --> C3["Analyst: Competitor Analysis (Optional)"]
    C3 --> D["Analyst: Create Project Brief"]
    D --> G
    G -->|Yes| E["PM: Create PRD from Brief (Fast Track)"]
    G -->|No| E2["PM: Interactive PRD Creation (More Questions)"]
    E --> F["PRD Created with FRs, NFRs, Epics & Stories"]
    E2 --> F
    F --> F2{"UX Required?"}
    F2 -->|Yes| F3["UX Expert: Create Front End Spec"]
    F2 -->|No| H["Architect: Create Architecture from PRD"]
    F3 --> F4["UX Expert: Generate UI Prompt for Lovable/V0 (Optional)"]
    F4 --> H2["Architect: Create Architecture from PRD + UX Spec"]
    H --> I["PO: Run Master Checklist"]
    H2 --> I
    I --> J{"Documents Aligned?"}
    J -->|Yes| K["Planning Complete"]
    J -->|No| L["PO: Update Epics & Stories"]
    L --> M["Update PRD/Architecture as needed"]
    M --> I
    K --> N["📁 Switch to IDE (If in a Web Agent Platform)"]
    N --> O["PO: Shard Documents"]
    O --> P["Ready for SM/Dev Cycle"]

    style A fill:#f5f5f5,color:#000
    style B fill:#e3f2fd,color:#000
    style C fill:#e8f5e9,color:#000
    style C2 fill:#e8f5e9,color:#000
    style C3 fill:#e8f5e9,color:#000
    style D fill:#e8f5e9,color:#000
    style E fill:#fff3e0,color:#000
    style E2 fill:#fff3e0,color:#000
    style F fill:#fff3e0,color:#000
    style F2 fill:#e3f2fd,color:#000
    style F3 fill:#e1f5fe,color:#000
    style F4 fill:#e1f5fe,color:#000
    style G fill:#e3f2fd,color:#000
    style H fill:#f3e5f5,color:#000
    style H2 fill:#f3e5f5,color:#000
    style I fill:#f9ab00,color:#fff
    style J fill:#e3f2fd,color:#000
    style K fill:#34a853,color:#fff
    style L fill:#f9ab00,color:#fff
    style M fill:#fff3e0,color:#000
    style N fill:#1a73e8,color:#fff
    style O fill:#f9ab00,color:#fff
    style P fill:#34a853,color:#fff
```

#### Web UI to IDE Transition

**Critical Transition Point**: Once the PO confirms document alignment, you must switch from web UI to IDE to begin the development workflow:

1. **Copy Documents to Project**: Ensure `docs/prd.md` and `docs/architecture.md` are in your project's docs folder (or a custom location you can specify during installation)
2. **Switch to IDE**: Open your project in your preferred Agentic IDE
3. **Document Sharding**: Use the PO agent to shard the PRD and then the Architecture
4. **Begin Development**: Start the Core Development Cycle that follows

### The Core Development Cycle (IDE)

Once planning is complete and documents are sharded, BMad follows a structured development workflow:

```mermaid
graph TD
    A["Development Phase Start"] --> B["SM: Reviews Previous Story Dev/QA Notes"]
    B --> B2["SM: Drafts Next Story from Sharded Epic + Architecture"]
    B2 --> B3{"PO: Validate Story Draft (Optional)"}
    B3 -->|Validation Requested| B4["PO: Validate Story Against Artifacts"]
    B3 -->|Skip Validation| C{"User Approval"}
    B4 --> C
    C -->|Approved| D["Dev: Sequential Task Execution"]
    C -->|Needs Changes| B2
    D --> E["Dev: Implement Tasks + Tests"]
    E --> F["Dev: Run All Validations"]
    F --> G["Dev: Mark Ready for Review + Add Notes"]
    G --> H{"User Verification"}
    H -->|Request QA Review| I["QA: Senior Dev Review + Active Refactoring"]
    H [CONTENT TRIMMED DUE TO SIZE]

#### (bmad-code-org/BMAD-METHOD/docs/expansion-packs.md)[https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/expansion-packs.md] (Score: 0.56)
- 
## Remember

The BMad Method is more than a development framework - it's a platform for structuring human expertise into AI-accessible formats. Every expansion pack you create makes specialized knowledge more accessible to everyone.

**What expertise will you share with the world?**

- # The Power of BMad Expansion Packs

## Overview

BMad Method's expansion packs unlock the framework's true potential by extending its natural language AI orchestration to ANY domain. While the core framework focuses on software development, expansion packs transform BMad into a universal AI agent system.

## Why Expansion Packs?

### Keep Core Lean

The core BMad framework maintains its focus on software development, ensuring dev agents have maximum context for coding. Expansion packs handle everything else.

### Domain Expertise

Each expansion pack provides deep, specialized knowledge without bloating the core system. Install only what you need.

### Community Innovation

Anyone can create and share expansion packs, fostering a ecosystem of AI-powered solutions across all industries and interests.

## Technical Expansion Packs

### Game Development Pack

Transform your AI into a complete game development studio:

- **Game Designer**: Mechanics, balance, progression systems
- **Level Designer**: Map layouts, puzzle design, difficulty curves
- **Narrative Designer**: Story arcs, dialog trees, lore creation
- **Art Director**: Visual style guides, asset specifications
- **Sound Designer**: Audio direction, music themes, SFX planning

### Mobile Development Pack

Specialized agents for mobile app creation:

- **iOS Specialist**: Swift/SwiftUI patterns, Apple guidelines
- **Android Expert**: Kotlin best practices, Material Design
- **Mobile UX Designer**: Touch interfaces, gesture patterns
- **App Store Optimizer**: ASO strategies, listing optimization
- **Performance Tuner**: Battery optimization, network efficiency

### DevOps/Infrastructure Pack

Complete infrastructure automation team:

- **Cloud Architect**: AWS/Azure/GCP design patterns
- **Security Specialist**: Zero-trust implementation, compliance
- **SRE Expert**: Monitoring, alerting, incident response
- **Container Orchestrator**: Kubernetes, Docker optimization
- **Cost Optimizer**: Cloud spend analysis, resource right-sizing

### Data Science Pack

AI-powered data analysis team:

- **Data Scientist**: Statistical analysis, ML model selection
- **Data Engineer**: Pipeline design, ETL processes
- **ML Engineer**: Model deployment, A/B testing
- **Visualization Expert**: Dashboard design, insight communication
- **Ethics Advisor**: Bias detection, fairness assessment

## Non-Technical Expansion Packs

### Business Strategy Pack

Complete business advisory team:

- **Strategy Consultant**: Market positioning, competitive analysis
- **Financial Analyst**: Projections, unit economics, funding strategies
- **Operations Manager**: Process optimization, efficiency improvements
- **Marketing Strategist**: Go-to-market plans, growth hacking
- **HR Advisor**: Talent strategies, culture building

### Creative Writing Pack

Your personal writing team:

- **Plot Architect**: Three-act structure, story beats, pacing
- **Character Psychologist**: Deep motivations, authentic dialog
- **World Builder**: Consistent universes, cultural systems
- **Editor**: Style consistency, grammar, flow
- **Beta Reader**: Feedback simulation, plot hole detection

### Health & Wellness Pack

Personal wellness coaching system:

- **Fitness Trainer**: Progressive overload, form correction
- **Nutritionist**: Macro planning, supplement guidance
- **Sleep Coach**: Circadian optimization, sleep hygiene
- **Stress Manager**: Coping strategies, work-life balance
- **Habit Engineer**: Behavior change, accountability systems

### Education Pack

Complete learning design system:

- **Curriculum Architect**: Learning objectives, scope & sequence
- **Instructional Designer**: Engagement strategies, multimedia learning
- **Assessment Specialist**: Rubrics, formative/summative evaluation
- **Differentiation Expert**: Adaptive learning, special needs
- **EdTech Integrator**: Tool selection, digital pedagogy

### Mental Health Support Pack

Therapeutic support system:

- **CBT Guide**: Cognitive restructuring, thought challenging
- **Mindfulness Teacher**: Meditation scripts, awareness exercises
- **Journal Therapist**: Reflective prompts, emotional processing
- **Crisis Support**: Coping strategies, safety planning
- **Habit Tracker**: Mood monitoring, trigger identification

### Legal Assistant Pack

Legal document and research support:

- **Contract Analyst**: Term review, risk assessment
- **Legal Researcher**: Case law, precedent analysis
- **Document Drafter**: Template customization, clause libraries
- **Compliance Checker**: Regulatory alignment, audit prep
- **IP Advisor**: Patent strategies, trademark guidance

### Real Estate Pack

Property investment and management:

- **Market Analyst**: Comparable analysis, trend prediction
- **Investment Calculator**: ROI modeling, cash flow analysis
- **Property Manager**: Tenant screening, maintenance scheduling
- **Flip Strategist**: Renovation ROI, project planning
- **Agent Assistant**: Listing optimization, showing prep

### Personal Developm [CONTENT TRIMMED DUE TO SIZE]

#### (bmad-code-org/BMAD-METHOD/docs/core-architecture.md)[https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/core-architecture.md] (Score: 0.55)
- 
#### 3.4.2. Technical Preferences System

BMad includes a personalization layer through the `technical-preferences.md` file in `bmad-core/data/`. This file serves as a persistent technical profile that influences agent behavior across all projects.

**Purpose and Benefits:**

- **Consistency**: Ensures all agents reference the same technical preferences
- **Efficiency**: Eliminates the need to repeatedly specify preferred technologies
- **Personalization**: Agents provide recommendations aligned with user preferences
- **Learning**: Captures lessons learned and preferences that evolve over time

**Content Structure:**
The file typically includes preferred technology stacks, design patterns, external services, coding standards, and anti-patterns to avoid. Agents automatically reference this file during planning and development to provide contextually appropriate suggestions.

**Integration Points:**

- Templates can reference technical preferences during document generation
- Agents suggest preferred technologies when appropriate for project requirements
- When preferences don't fit project needs, agents explain alternatives
- Web bundles can include preferences content for consistent behavior across platforms

**Evolution Over Time:**
Users are encouraged to continuously update this file with discoveries from projects, adding both positive preferences and technologies to avoid, creating a personalized knowledge base that improves agent recommendations over time.


- # BMad Method: Core Architecture

## 1. Overview

The BMad Method is designed to provide agentic modes, tasks and templates to allow repeatable helpful workflows be it for agile agentic development, or expansion into vastly different domains. The core purpose of the project is to provide a structured yet flexible set of prompts, templates, and workflows that users can employ to guide AI agents (like Gemini, Claude, or ChatGPT) to perform complex tasks, guided discussions, or other meaningful domain specific flows in a predictable, high-quality manner.

The systems core module facilitates a full development lifecycle tailored to the challenges of current modern AI Agentic tooling:

1. **Ideation & Planning**: Brainstorming, market research, and creating project briefs.
2. **Architecture & Design**: Defining system architecture and UI/UX specifications.
3. **Development Execution**: A cyclical workflow where a Scrum Master (SM) agent drafts stories with extremely specific context and a Developer (Dev) agent implements them one at a time. This process works for both new (Greenfield) and existing (Brownfield) projects.

## 2. System Architecture Diagram

The entire BMad-Method ecosystem is designed around the installed `bmad-core` directory, which acts as the brain of the operation. The `tools` directory provides the means to process and package this brain for different environments.

```mermaid
graph TD
    subgraph BMad Method Project
        subgraph Core Framework
            A["bmad-core"]
            A --> B["agents"]
            A --> C["agent-teams"]
            A --> D["workflows"]
            A --> E["templates"]
            A --> F["tasks"]
            A --> G["checklists"]
            A --> H["data (KB)"]
        end

        subgraph Tooling
            I["tools/builders/web-builder.js"]
        end

        subgraph Outputs
            J["dist"]
        end

        B -- defines dependencies for --> E
        B -- defines dependencies for --> F
        B -- defines dependencies for --> G
        B -- defines dependencies for --> H

        C -- bundles --> B
        I -- reads from --> A
        I -- creates --> J
    end

    subgraph Target Environments
        K["IDE (Cursor, VS Code, etc.)"]
        L["Web UI (Gemini, ChatGPT)"]
    end

    B --> K
    J --> L

    style A fill:#1a73e8,color:#fff
    style I fill:#f9ab00,color:#fff
    style J fill:#34a853,color:#fff
```

## 3. Core Components

The `bmad-core` directory contains all the definitions and resources that give the agents their capabilities.

### 3.1. Agents (`bmad-core/agents/`)

- **Purpose**: These are the foundational building blocks of the system. Each markdown file (e.g., `bmad-master.md`, `pm.md`, `dev.md`) defines the persona, capabilities, and dependencies of a single AI agent.
- **Structure**: An agent file contains a YAML header that specifies its role, persona, dependencies, and startup instructions. These dependencies are lists of tasks, templates, checklists, and data files that the agent is allowed to use.
- **Startup Instructions**: Agents can include startup sequences that load project-specific documentation from the `docs/` folder, such as coding standards, API specifications, or project structure documents. This provides immediate project context upon activation.
- **Document Integration**: Agents can reference and load documents from the project's `docs/` folder as part of tasks, workflows, or startup sequences. Users can also drag documents directly into chat interfaces to provide additional context.
- **Example**: The `bmad-master` agent lists its dependencies, which tells the build tool which files to include in a web bundle and informs the agent of its own capabilities.

### 3.2. Agent Teams (`bmad-core/agent-teams/`)

- **Purpose**: Team files (e.g., `team-all.yaml`) define collections of agents and workflows that are bundled together for a specific purpose, like "full-stack development" or "backend-only". This creates a larger, pre-packaged context for web UI environments.
- **Structure**: A team file lists the agents to include. It can use wildcards, such as `"*"` to include all agents. This allows for the creation of comprehensive bundles like `team-all`.

### 3.3. Workflows (`bmad-core/workflows/`)

- **Purpose**: Workflows are YAML files (e.g., `greenfield-fullstack.yaml`) that define a prescribed sequence of steps and agent interactions for a specific project type. They act as a strategic guide for the user and the `bmad-orchestrator` agent.
- **Structure**: A workflow defines sequences for both complex and simple projects, lists the agents involved at each step, the artifacts they create, and the conditions for moving from one step to the next. It often includes a Mermaid diagram for visualization.

### 3.4. Reusable Resources (`templates`, `tasks`, `checklists`, `data`)

- **Purpose**: These folders house the modular components that are dynamically loaded by agents based on their dependencies.
  - * [CONTENT TRIMMED DUE TO SIZE]
- 
#### 3.4.1. Template Processing System

A key architectural principle of BMad is that templates are self-contained and interactive - they embed both the desired document output and the LLM instructions needed to work with users. This means that in many cases, no separate task is needed for document creation, as the template itself contains all the processing logic.

The BMad framework employs a sophisticated template processing system orchestrated by three key components:

- **`template-format.md`** (`bmad-core/utils/`): Defines the foundational markup language used throughout all BMad templates. This specification establishes syntax rules for variable substitution (`{{placeholders}}`), AI-only processing directives (`[[LLM: instructions]]`), and conditional logic blocks. Templates follow this format to ensure consistent processing across the system.

- **`create-doc.md`** (`bmad-core/tasks/`): Acts as the orchestration engine that manages the entire document generation workflow. This task coordinates template selection, manages user interaction modes (incremental vs. rapid generation), enforces template-format processing rules, and handles validation. It serves as the primary interface between users and the template system.

- **`advanced-elicitation.md`** (`bmad-core/tasks/`): Provides an interactive refinement layer that can be embedded within templates through `[[LLM: instructions]]` blocks. This component offers 10 structured brainstorming actions, section-by-section review capabilities, and iterative improvement workflows to enhance content quality.

The system maintains a clean separation of concerns: template markup is processed internally by AI agents but never exposed to users, while providing sophisticated AI processing capabilities through embedded intelligence within the templates themselves.



</details>

---

<details>
          <summary>MCP tool call</summary>
      
*Called tool*: BMAD-METHOD_Docs - search_BMAD_METHOD_documentation: Semantically search within the fetched documentation from GitHub repository: bmad-code-org/BMAD-METHOD. Useful for specific queries.

*Parameters*:
  * query: agents and agent teams capabilities

*Result*:
## Query

agents and agent teams capabilities.

## Response

### Sources:
Important: you can fetch the full content of any source using the fetch_url_content tool

#### (bmad-code-org/BMAD-METHOD/docs/GUIDING-PRINCIPLES.md)[https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/GUIDING-PRINCIPLES.md] (Score: 0.53)
- 
## Remember

- The power is in natural language orchestration, not code
- Dev agents code, planning agents plan
- Keep dev agents lean for maximum coding efficiency
- Expansion packs handle specialized domains

- 
## Practical Guidelines

### When to Add to Core

- Universal software development needs only
- Doesn't bloat dev agent contexts
- Follows existing agent/task/template patterns

### When to Create Expansion Packs

- Domain-specific needs beyond software development
- Non-technical domains (business, wellness, education, creative)
- Specialized technical domains (games, infrastructure, mobile)
- Heavy documentation or knowledge bases
- Anything that would bloat core agents

See [Expansion Packs Guide](../docs/expansion-packs.md) for detailed examples and ideas.

### Agent Design Rules

1. **Web/Planning Agents**: Can have richer context, multiple tasks, extensive templates
2. **Dev Agents**: Minimal dependencies, focused on code generation, lean task sets
3. **All Agents**: Clear persona, specific expertise, well-defined capabilities

### Task Writing Rules

1. Write clear step-by-step procedures
2. Use markdown formatting for readability
3. Keep dev agent tasks focused and concise
4. Planning tasks can be more elaborate
5. **Prefer multiple small tasks over one large branching task**
   - Instead of one task with many conditional paths
   - Create multiple focused tasks the agent can choose from
   - This keeps context overhead minimal
6. **Reuse common tasks** - Don't create new document creation tasks
   - Use the existing `create-doc` task
   - Pass the appropriate YAML template with structured sections
   - This maintains consistency and reduces duplication



#### (bmad-code-org/BMAD-METHOD/llms.txt)[https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/llms.txt] (Score: 0.51)
- 

KEY TERMS:
1. BMad Method
2. Story Creation
3. Scrum Master
4. Developer
5. Quality Assurance
6. Branch Creation
7. Agent Execution
8. Checklist Task
9. Story Implementation
10. Story Review


- [expansion-packs.md](https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/expansion-packs.md): BMad Expansion Packs: Unlocking AI Potential Across Domains
The BMad Method's expansion packs extend the framework's natural language AI orchestration to various domains, transforming it into a universal AI agent system. The core framework focuses on software development, while expansion packs handle other areas. There are technical packs (Game Development, Mobile Development, DevOps, Data Science) and non-technical packs (Business Strategy, Creative Writing, Health & Wellness, Education). Each pack provides specialized knowledge and can be installed as needed. Users can create their own packs by defining a domain, designing agents, creating tasks, building templates, testing, and sharing. The future of expansion packs includes a marketplace, AI agent ecosystems, and democratizing expertise.

No code examples are provided in the document.

KEY TERMS:
1. BMad Method
2. Expansion Packs
3. AI Orchestration
4. Natural Language AI
5. Game Development Pack
6. Mobile Development Pack
7. DevOps Pack
8. Data Science Pack
9. Business Strategy Pack
10. AI Agent Ecosystems

- 
# Example of template structure
template:
  id: example-template
  name: Example Template
  version: 1.0
  output:
    format: markdown
workflow:
  interactionMode: guided
sections:
  - id: introduction
    title: Introduction
    instruction: Write a brief introduction
```

The document also mentions the use of variable substitution, conditional sections, and repeatable sections in templates.

Some additional code examples are provided in the document, including YAML templates with structured sections and metadata.

KEY TERMS:

1. BMad Method
2. Natural Language Framework
3. AI-Assisted Software Development
4. Dev Agents
5. Planning Agents
6. Templates
7. Expansion Packs
8. YAML Format
9. Agent Design
10. Task Writing


- [core-architecture.md](https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/core-architecture.md): BMad Method Core Architecture Overview
The BMad Method is a structured framework designed to facilitate agentic modes, tasks, and templates for repeatable and helpful workflows. It provides a full development lifecycle tailored to modern AI agentic tooling, covering ideation, planning, architecture, design, and development execution. The core module, `bmad-core`, acts as the brain of the operation and is supported by the `tools` directory for processing and packaging. The framework includes agents, agent teams, workflows, templates, tasks, checklists, and data, which are dynamically loaded based on agent dependencies. A key feature is the template processing system, which allows for self-contained and interactive templates. The framework also includes a technical preferences system for personalized agent behavior.

```markdown
Example of template processing system components:
- `template-format.md`: Defines the foundational markup language for BMad templates.
- `create-doc.md`: Manages the document generation workflow.

- 

Example of BMad workflow:
graph TD
    A["Start: Project Idea"] --> B{"Optional: Analyst Brainstorming"}
    B -->|Yes| C["Analyst: Market Research & Analysis"]
```

Note that there are additional code examples and diagrams throughout the document, including Mermaid diagrams for system architecture and workflows.

KEY TERMS:
1. BMad Method
2. Agentic modes
3. Template processing system
4. Technical preferences system
5. Agent teams
6. Workflows
7. BMad-core
8. Web-builder.js
9. Development lifecycle
10. AI agentic tooling


- [enhanced-ide-development-workflow.md](https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/enhanced-ide-development-workflow.md): Enhanced Development Workflow with BMad Method
This document outlines a step-by-step guide to efficiently manage the development workflow using the BMad Method. It covers creating a new branch, story creation, implementation, review, and committing changes. The guide is divided into sections for Scrum Master, Developer, and Quality Assurance roles. Key technical terms include BMad Method, story creation, and agent execution. The document provides commands for executing tasks, such as `*draft` for creating a new story and `*develop-story {selected-story}` for implementing a story. 

Example commands:
```markdown
*draft (runs create-next-story task)
*develop-story {selected-story} (runs execute-checklist task)
```
The document also mentions additional commands, such as `*review {selected-story}` for reviewing a story.

KEY TERMS:
1. BMad Method
2. Story Creation
3. Scrum Master
4. Developer
5. Quality Assurance
6. Branch Creation
7. Agent Execution
8. Checklist Task
9. Story Implementation
10. Story Review


#### (bmad-code-org/BMAD-METHOD/docs/core-architecture.md)[https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/core-architecture.md] (Score: 0.51)
- # BMad Method: Core Architecture

## 1. Overview

The BMad Method is designed to provide agentic modes, tasks and templates to allow repeatable helpful workflows be it for agile agentic development, or expansion into vastly different domains. The core purpose of the project is to provide a structured yet flexible set of prompts, templates, and workflows that users can employ to guide AI agents (like Gemini, Claude, or ChatGPT) to perform complex tasks, guided discussions, or other meaningful domain specific flows in a predictable, high-quality manner.

The systems core module facilitates a full development lifecycle tailored to the challenges of current modern AI Agentic tooling:

1. **Ideation & Planning**: Brainstorming, market research, and creating project briefs.
2. **Architecture & Design**: Defining system architecture and UI/UX specifications.
3. **Development Execution**: A cyclical workflow where a Scrum Master (SM) agent drafts stories with extremely specific context and a Developer (Dev) agent implements them one at a time. This process works for both new (Greenfield) and existing (Brownfield) projects.

## 2. System Architecture Diagram

The entire BMad-Method ecosystem is designed around the installed `bmad-core` directory, which acts as the brain of the operation. The `tools` directory provides the means to process and package this brain for different environments.

```mermaid
graph TD
    subgraph BMad Method Project
        subgraph Core Framework
            A["bmad-core"]
            A --> B["agents"]
            A --> C["agent-teams"]
            A --> D["workflows"]
            A --> E["templates"]
            A --> F["tasks"]
            A --> G["checklists"]
            A --> H["data (KB)"]
        end

        subgraph Tooling
            I["tools/builders/web-builder.js"]
        end

        subgraph Outputs
            J["dist"]
        end

        B -- defines dependencies for --> E
        B -- defines dependencies for --> F
        B -- defines dependencies for --> G
        B -- defines dependencies for --> H

        C -- bundles --> B
        I -- reads from --> A
        I -- creates --> J
    end

    subgraph Target Environments
        K["IDE (Cursor, VS Code, etc.)"]
        L["Web UI (Gemini, ChatGPT)"]
    end

    B --> K
    J --> L

    style A fill:#1a73e8,color:#fff
    style I fill:#f9ab00,color:#fff
    style J fill:#34a853,color:#fff
```

## 3. Core Components

The `bmad-core` directory contains all the definitions and resources that give the agents their capabilities.

### 3.1. Agents (`bmad-core/agents/`)

- **Purpose**: These are the foundational building blocks of the system. Each markdown file (e.g., `bmad-master.md`, `pm.md`, `dev.md`) defines the persona, capabilities, and dependencies of a single AI agent.
- **Structure**: An agent file contains a YAML header that specifies its role, persona, dependencies, and startup instructions. These dependencies are lists of tasks, templates, checklists, and data files that the agent is allowed to use.
- **Startup Instructions**: Agents can include startup sequences that load project-specific documentation from the `docs/` folder, such as coding standards, API specifications, or project structure documents. This provides immediate project context upon activation.
- **Document Integration**: Agents can reference and load documents from the project's `docs/` folder as part of tasks, workflows, or startup sequences. Users can also drag documents directly into chat interfaces to provide additional context.
- **Example**: The `bmad-master` agent lists its dependencies, which tells the build tool which files to include in a web bundle and informs the agent of its own capabilities.

### 3.2. Agent Teams (`bmad-core/agent-teams/`)

- **Purpose**: Team files (e.g., `team-all.yaml`) define collections of agents and workflows that are bundled together for a specific purpose, like "full-stack development" or "backend-only". This creates a larger, pre-packaged context for web UI environments.
- **Structure**: A team file lists the agents to include. It can use wildcards, such as `"*"` to include all agents. This allows for the creation of comprehensive bundles like `team-all`.

### 3.3. Workflows (`bmad-core/workflows/`)

- **Purpose**: Workflows are YAML files (e.g., `greenfield-fullstack.yaml`) that define a prescribed sequence of steps and agent interactions for a specific project type. They act as a strategic guide for the user and the `bmad-orchestrator` agent.
- **Structure**: A workflow defines sequences for both complex and simple projects, lists the agents involved at each step, the artifacts they create, and the conditions for moving from one step to the next. It often includes a Mermaid diagram for visualization.

### 3.4. Reusable Resources (`templates`, `tasks`, `checklists`, `data`)

- **Purpose**: These folders house the modular components that are dynamically loaded by agents based on their dependencies.
  - * [CONTENT TRIMMED DUE TO SIZE]
- 
## 3. Core Components

The `bmad-core` directory contains all the definitions and resources that give the agents their capabilities.

### 3.1. Agents (`bmad-core/agents/`)

- **Purpose**: These are the foundational building blocks of the system. Each markdown file (e.g., `bmad-master.md`, `pm.md`, `dev.md`) defines the persona, capabilities, and dependencies of a single AI agent.
- **Structure**: An agent file contains a YAML header that specifies its role, persona, dependencies, and startup instructions. These dependencies are lists of tasks, templates, checklists, and data files that the agent is allowed to use.
- **Startup Instructions**: Agents can include startup sequences that load project-specific documentation from the `docs/` folder, such as coding standards, API specifications, or project structure documents. This provides immediate project context upon activation.
- **Document Integration**: Agents can reference and load documents from the project's `docs/` folder as part of tasks, workflows, or startup sequences. Users can also drag documents directly into chat interfaces to provide additional context.
- **Example**: The `bmad-master` agent lists its dependencies, which tells the build tool which files to include in a web bundle and informs the agent of its own capabilities.

### 3.2. Agent Teams (`bmad-core/agent-teams/`)

- **Purpose**: Team files (e.g., `team-all.yaml`) define collections of agents and workflows that are bundled together for a specific purpose, like "full-stack development" or "backend-only". This creates a larger, pre-packaged context for web UI environments.
- **Structure**: A team file lists the agents to include. It can use wildcards, such as `"*"` to include all agents. This allows for the creation of comprehensive bundles like `team-all`.



#### (bmad-code-org/BMAD-METHOD/docs/expansion-packs.md)[https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/expansion-packs.md] (Score: 0.49)
- 
## Creating Your Own Expansion Pack

### Step 1: Define Your Domain

What expertise are you capturing? What problems will it solve?

### Step 2: Design Your Agents

Each agent should have:

- Clear expertise area
- Specific personality traits
- Defined capabilities
- Knowledge boundaries

### Step 3: Create Tasks

Tasks should be:

- Step-by-step procedures
- Reusable across scenarios
- Clear and actionable
- Domain-specific

### Step 4: Build Templates

Templates need:

- Structured output format
- Embedded LLM instructions
- Placeholders for customization
- Professional formatting

### Step 5: Test & Iterate

- Use with real scenarios
- Gather user feedback
- Refine agent responses
- Improve task clarity

### Step 6: Package & Share

- Create clear documentation
- Include usage examples
- Add to expansion-packs directory
- Share with community

## The Future of Expansion Packs

### Marketplace Potential

Imagine a future where:

- Professional expansion packs are sold
- Certified packs for regulated industries
- Community ratings and reviews
- Automatic updates and improvements

### AI Agent Ecosystems

Expansion packs could enable:

- Cross-pack agent collaboration
- Industry-standard agent protocols
- Interoperable AI workflows
- Universal agent languages

### Democratizing Expertise

Every expansion pack:

- Makes expert knowledge accessible
- Reduces barriers to entry
- Enables solo entrepreneurs
- Empowers small teams

## Getting Started

1. **Browse existing packs**: Check `expansion-packs/` directory
2. **Install what you need**: Use the installer's expansion pack option
3. **Create your own**: Use the expansion-creator pack
4. **Share with others**: Submit PRs with new packs
5. **Build the future**: Help shape AI-assisted work


- 
## Technical Expansion Packs

### Game Development Pack

Transform your AI into a complete game development studio:

- **Game Designer**: Mechanics, balance, progression systems
- **Level Designer**: Map layouts, puzzle design, difficulty curves
- **Narrative Designer**: Story arcs, dialog trees, lore creation
- **Art Director**: Visual style guides, asset specifications
- **Sound Designer**: Audio direction, music themes, SFX planning

### Mobile Development Pack

Specialized agents for mobile app creation:

- **iOS Specialist**: Swift/SwiftUI patterns, Apple guidelines
- **Android Expert**: Kotlin best practices, Material Design
- **Mobile UX Designer**: Touch interfaces, gesture patterns
- **App Store Optimizer**: ASO strategies, listing optimization
- **Performance Tuner**: Battery optimization, network efficiency

### DevOps/Infrastructure Pack

Complete infrastructure automation team:

- **Cloud Architect**: AWS/Azure/GCP design patterns
- **Security Specialist**: Zero-trust implementation, compliance
- **SRE Expert**: Monitoring, alerting, incident response
- **Container Orchestrator**: Kubernetes, Docker optimization
- **Cost Optimizer**: Cloud spend analysis, resource right-sizing

### Data Science Pack

AI-powered data analysis team:

- **Data Scientist**: Statistical analysis, ML model selection
- **Data Engineer**: Pipeline design, ETL processes
- **ML Engineer**: Model deployment, A/B testing
- **Visualization Expert**: Dashboard design, insight communication
- **Ethics Advisor**: Bias detection, fairness assessment


- 
### Legal Assistant Pack

Legal document and research support:

- **Contract Analyst**: Term review, risk assessment
- **Legal Researcher**: Case law, precedent analysis
- **Document Drafter**: Template customization, clause libraries
- **Compliance Checker**: Regulatory alignment, audit prep
- **IP Advisor**: Patent strategies, trademark guidance

### Real Estate Pack

Property investment and management:

- **Market Analyst**: Comparable analysis, trend prediction
- **Investment Calculator**: ROI modeling, cash flow analysis
- **Property Manager**: Tenant screening, maintenance scheduling
- **Flip Strategist**: Renovation ROI, project planning
- **Agent Assistant**: Listing optimization, showing prep

### Personal Development Pack

Complete personal growth system:

- **Life Coach**: Guides personal growth and transformation
- **Goal Strategist**: Helps achieve objectives with SMART goals
- **Habit Builder**: Creates lasting habits with accountability
- **Mindset Mentor**: Develops positive thinking patterns

Key tasks include:

- `goal-setting`: Defines SMART goals with action plans
- `habit-tracking`: Monitors habit formation progress
- `reflection-exercise`: Facilitates deep self-reflection



#### (bmad-code-org/BMAD-METHOD/docs/user-guide.md)[https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/user-guide.md] (Score: 0.48)
- sh
# Interactive installation (recommended)
npx bmad-method install
```

## Special Agents

There are two bmad agents - in the future they will be consolidated into the single bmad-master.

### BMad-Master

This agent can do any task or command that all other agents can do, aside from actual story implementation. Additionally, this agent can help explain the BMad Method when in the web by accessing the knowledge base and explaining anything to you about the process.

If you don't want to bother switching between different agents aside from the dev, this is the agent for you. Just remember that as the context grows, the performance of the agent degrades, therefore it is important to instruct the agent to compact the conversation and start a new conversation with the compacted conversation as the initial message. Do this often, preferably after each story is implemented.

### BMad-Orchestrator

This agent should NOT be used within the IDE, it is a heavy weight special purpose agent that utilizes a lot of context and can morph into any other agent. This exists solely to facilitate the team's within the web bundles. If you use a web bundle you will be greeted by the BMad Orchestrator.

### How Agents Work

#### Dependencies System

Each agent has a YAML section that defines its dependencies:

```yaml
dependencies:
  templates:
    - prd-template.md
    - user-story-template.md
  tasks:
    - create-doc.md
    - shard-doc.md
  data:
    - bmad-kb.md
```

**Key Points:**

- Agents only load resources they need (lean context)
- Dependencies are automatically resolved during bundling
- Resources are shared across agents to maintain consistency

#### Agent Interaction

**In IDE:**

```bash
# Some Ide's, like Cursor or Windsurf for example, utilize manual rules so interaction is done with the '@' symbol
@pm Create a PRD for a task management app
@architect Design the system architecture
@dev Implement the user authentication
- n

# Some, like Claude Code use slash commands instead
/pm Create user stories
/dev Fix the login bug
```

#### Interactive Modes

- **Incremental Mode**: Step-by-step with user input
- **YOLO Mode**: Rapid generation with minimal interaction

## IDE Integration

### IDE Best Practices

- **Context Management**: Keep relevant files only in context, keep files as lean and focused as necessary
- **Agent Selection**: Use appropriate agent for task
- **Iterative Development**: Work in small, focused tasks
- **File Organization**: Maintain clean project structure
- **Commit Regularly**: Save your work frequently

## Technical Preferences System

BMad includes a personalization system through the `technical-preferences.md` file located in `.bmad-core/data/` - this can help bias the PM and Architect to recommend your preferences for design patterns, technology selection, or anything else you would like to put in here.

### Using with Web Bundles

When creating custom web bundles or uploading to AI platforms, include your `technical-preferences.md` content to ensure agents have your preferences from the start of any conversation.

</details>

---

<details>
          <summary>MCP tool call</summary>
      
*Called tool*: BMAD-METHOD_Docs - search_BMAD_METHOD_documentation: Semantically search within the fetched documentation from GitHub repository: bmad-code-org/BMAD-METHOD. Useful for specific queries.

*Parameters*:
  * query: workflows lifecycle planning development

*Result*:
## Query

workflows lifecycle planning development.

## Response

### Sources:
Important: you can fetch the full content of any source using the fetch_url_content tool

#### (bmad-code-org/BMAD-METHOD/docs/core-architecture.md)[https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/core-architecture.md] (Score: 0.59)
- 
### 3.3. Workflows (`bmad-core/workflows/`)

- **Purpose**: Workflows are YAML files (e.g., `greenfield-fullstack.yaml`) that define a prescribed sequence of steps and agent interactions for a specific project type. They act as a strategic guide for the user and the `bmad-orchestrator` agent.
- **Structure**: A workflow defines sequences for both complex and simple projects, lists the agents involved at each step, the artifacts they create, and the conditions for moving from one step to the next. It often includes a Mermaid diagram for visualization.


- .

### 5.2. The Core Development Cycle

Once the initial planning and architecture phases are complete, the project moves into a cyclical development workflow, as detailed in the `bmad-kb.md`. This ensures a steady, sequential, and quality-controlled implementation process.

```mermaid
graph TD
    A["Start: Planning Artifacts Complete"] --> B["PO: Shard Epics"]
    B --> C["PO: Shard Arch"]
    C --> D["Development Phase"]
    D --> E["Scrum Master: Drafts next story from sharded epic"]
    E --> F{"User Approval"}
    F -->|Approved| G["Dev: Implement Story"]
    F -->|Needs Changes| E
    G --> H["Dev: Complete story Tasks"]
    H --> I["Dev: Mark Ready for Review"]
    I --> J{"User Verification"}
    J -->|Request QA Review| K["QA: Run review-story task"]
    J -->|Approve Without QA| M["Mark Story as Done"]
    K --> L{"QA Review Results"}
    L -->|Needs Work| G
    L -->|Approved| M["Mark Story as Done"]
    J -->|Needs Fixes| G
    M --> E

    style M fill:#34a853,color:#fff
    style K fill:#f9ab00,color:#fff
```

This cycle continues, with the Scrum Master, Developer, and optionally QA agents working together. The QA agent provides senior developer review capabilities through the `review-story` task, offering code refactoring, quality improvements, and knowledge transfer. This ensures high code quality while maintaining development velocity.
- 
## 5. BMad Workflows

### 5.1. The Planning Workflow

Before development begins, BMad follows a structured planning workflow that establishes the foundation for successful project execution:

```mermaid
graph TD
    A["Start: Project Idea"] --> B{"Optional: Analyst Brainstorming"}
    B -->|Yes| C["Analyst: Market Research & Analysis"]
    B -->|No| D["Create Project Brief"]
    C --> D["Analyst: Create Project Brief"]
    D --> E["PM: Create PRD from Brief"]
    E --> F["Architect: Create Architecture from PRD"]
    F --> G["PO: Run Master Checklist"]
    G --> H{"Documents Aligned?"}
    H -->|Yes| I["Planning Complete"]
    H -->|No| J["PO: Update Epics & Stories"]
    J --> K["Update PRD/Architecture as needed"]
    K --> G
    I --> L["📁 Switch to IDE"]
    L --> M["PO: Shard Documents"]
    M --> N["Ready for SM/Dev Cycle"]

    style I fill:#34a853,color:#fff
    style G fill:#f9ab00,color:#fff
    style L fill:#1a73e8,color:#fff
    style N fill:#34a853,color:#fff
```

**Key Planning Phases:**

1. **Optional Analysis**: Analyst conducts market research and competitive analysis
2. **Project Brief**: Foundation document created by Analyst or user
3. **PRD Creation**: PM transforms brief into comprehensive product requirements
4. **Architecture Design**: Architect creates technical foundation based on PRD
5. **Validation & Alignment**: PO ensures all documents are consistent and complete
6. **Refinement**: Updates to epics, stories, and documents as needed
7. **Environment Transition**: Critical switch from web UI to IDE for development workflow
8. **Document Preparation**: PO shards large documents for development consumption

**Workflow Orchestration**: The `bmad-orchestrator` agent uses these workflow definitions to guide users through the complete process, ensuring proper transitions between planning (web UI) and development (IDE) phases.
- # BMad Method: Core Architecture

## 1. Overview

The BMad Method is designed to provide agentic modes, tasks and templates to allow repeatable helpful workflows be it for agile agentic development, or expansion into vastly different domains. The core purpose of the project is to provide a structured yet flexible set of prompts, templates, and workflows that users can employ to guide AI agents (like Gemini, Claude, or ChatGPT) to perform complex tasks, guided discussions, or other meaningful domain specific flows in a predictable, high-quality manner.

The systems core module facilitates a full development lifecycle tailored to the challenges of current modern AI Agentic tooling:

1. **Ideation & Planning**: Brainstorming, market research, and creating project briefs.
2. **Architecture & Design**: Defining system architecture and UI/UX specifications.
3. **Development Execution**: A cyclical workflow where a Scrum Master (SM) agent drafts stories with extremely specific context and a Developer (Dev) agent implements them one at a time. This process works for both new (Greenfield) and existing (Brownfield) projects.

## 2. System Architecture Diagram

The entire BMad-Method ecosystem is designed around the installed `bmad-core` directory, which acts as the brain of the operation. The `tools` directory provides the means to process and package this brain for different environments.

```mermaid
graph TD
    subgraph BMad Method Project
        subgraph Core Framework
            A["bmad-core"]
            A --> B["agents"]
            A --> C["agent-teams"]
            A --> D["workflows"]
            A --> E["templates"]
            A --> F["tasks"]
            A --> G["checklists"]
            A --> H["data (KB)"]
        end

        subgraph Tooling
            I["tools/builders/web-builder.js"]
        end

        subgraph Outputs
            J["dist"]
        end

        B -- defines dependencies for --> E
        B -- defines dependencies for --> F
        B -- defines dependencies for --> G
        B -- defines dependencies for --> H

        C -- bundles --> B
        I -- reads from --> A
        I -- creates --> J
    end

    subgraph Target Environments
        K["IDE (Cursor, VS Code, etc.)"]
        L["Web UI (Gemini, ChatGPT)"]
    end

    B --> K
    J --> L

    style A fill:#1a73e8,color:#fff
    style I fill:#f9ab00,color:#fff
    style J fill:#34a853,color:#fff
```

## 3. Core Components

The `bmad-core` directory contains all the definitions and resources that give the agents their capabilities.

### 3.1. Agents (`bmad-core/agents/`)

- **Purpose**: These are the foundational building blocks of the system. Each markdown file (e.g., `bmad-master.md`, `pm.md`, `dev.md`) defines the persona, capabilities, and dependencies of a single AI agent.
- **Structure**: An agent file contains a YAML header that specifies its role, persona, dependencies, and startup instructions. These dependencies are lists of tasks, templates, checklists, and data files that the agent is allowed to use.
- **Startup Instructions**: Agents can include startup sequences that load project-specific documentation from the `docs/` folder, such as coding standards, API specifications, or project structure documents. This provides immediate project context upon activation.
- **Document Integration**: Agents can reference and load documents from the project's `docs/` folder as part of tasks, workflows, or startup sequences. Users can also drag documents directly into chat interfaces to provide additional context.
- **Example**: The `bmad-master` agent lists its dependencies, which tells the build tool which files to include in a web bundle and informs the agent of its own capabilities.

### 3.2. Agent Teams (`bmad-core/agent-teams/`)

- **Purpose**: Team files (e.g., `team-all.yaml`) define collections of agents and workflows that are bundled together for a specific purpose, like "full-stack development" or "backend-only". This creates a larger, pre-packaged context for web UI environments.
- **Structure**: A team file lists the agents to include. It can use wildcards, such as `"*"` to include all agents. This allows for the creation of comprehensive bundles like `team-all`.

### 3.3. Workflows (`bmad-core/workflows/`)

- **Purpose**: Workflows are YAML files (e.g., `greenfield-fullstack.yaml`) that define a prescribed sequence of steps and agent interactions for a specific project type. They act as a strategic guide for the user and the `bmad-orchestrator` agent.
- **Structure**: A workflow defines sequences for both complex and simple projects, lists the agents involved at each step, the artifacts they create, and the conditions for moving from one step to the next. It often includes a Mermaid diagram for visualization.

### 3.4. Reusable Resources (`templates`, `tasks`, `checklists`, `data`)

- **Purpose**: These folders house the modular components that are dynamically loaded by agents based on their dependencies.
  - * [CONTENT TRIMMED DUE TO SIZE]

#### (bmad-code-org/BMAD-METHOD/docs/enhanced-ide-development-workflow.md)[https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/enhanced-ide-development-workflow.md] (Score: 0.51)
- # Enhanced Development Workflow

This is a simple step-by-step guide to help you efficiently manage your development workflow using the BMad Method. Refer to the **[<ins>User Guide</ins>](user-guide.md)** for any scenario that is not covered here.

## Create new Branch

1. **Start new branch**

## Story Creation (Scrum Master)

1. **Start new chat/conversation**
2. **Load SM agent**
3. **Execute**: `*draft` (runs create-next-story task)
4. **Review generated story** in `docs/stories/`
5. **Update status**: Change from "Draft" to "Approved"

## Story Implementation (Developer)

1. **Start new chat/conversation**
2. **Load Dev agent**
3. **Execute**: `*develop-story {selected-story}` (runs execute-checklist task)
4. **Review generated report** in `{selected-story}`

## Story Review (Quality Assurance)

1. **Start new chat/conversation**
2. **Load QA agent**
3. **Execute**: `*review {selected-story}` (runs review-story task)
4. **Review generated report** in `{selected-story}`

## Commit Changes and Push

1. **Commit changes**
2. **Push to remote**

## Repeat Until Complete

- **SM**: Create next story → Review → Approve
- **Dev**: Implement story → Complete → Mark Ready for Review
- **QA**: Review story → Mark done
- **Commit**: All changes
- **Push**: To remote
- **Continue**: Until all features implemented


#### (bmad-code-org/BMAD-METHOD/docs/working-in-the-brownfield.md)[https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/working-in-the-brownfield.md] (Score: 0.50)
- 
### Approach B: Document-First Workflow

#### Phase 1: Document the Existing System

**Best Approach - Gemini Web with 1M+ Context**:

1. **Go to Gemini Web** (gemini.google.com)
2. **Upload your project**:
   - **Option A**: Paste your GitHub repository URL directly
   - **Option B**: Upload your flattened-codebase.xml file
3. **Load the analyst agent**: Upload `dist/agents/architect.txt`
4. **Run documentation**: Type `*document-project`

The analyst will generate comprehensive documentation of everything.


- 
#### Phase 2: Plan Your Enhancement

##### Option A: Full Brownfield Workflow (Recommended for Major Changes)

**1. Create Brownfield PRD**:

```bash
@pm
*create-brownfield-prd
```

The PM agent will:

- **Analyze existing documentation** from Phase 1
- **Request specific enhancement details** from you
- **Assess complexity** and recommend approach
- **Create epic/story structure** for the enhancement
- **Identify risks and integration points**

**How PM Agent Gets Project Context**:

- In Gemini Web: Already has full project context from Phase 1 documentation
- In IDE: Will ask "Please provide the path to your existing project documentation"

**Key Prompts You'll Encounter**:

- "What specific enhancement or feature do you want to add?"
- "Are there any existing systems or APIs this needs to integrate with?"
- "What are the critical constraints we must respect?"
- "What is your timeline and team size?"

**2. Create Brownfield Architecture**:

```bash
@architect
*create-brownfield-architecture
```

The architect will:

- **Review the brownfield PRD**
- **Design integration strategy**
- **Plan migration approach** if needed
- **Identify technical risks**
- **Define compatibility requirements**

##### Option B: Quick Enhancement (For Focused Changes)

**For Single Epic Without Full PRD**:

```bash
@pm
*create-brownfield-epic
```

Use when:

- Enhancement is well-defined and isolated
- Existing documentation is comprehensive
- Changes don't impact multiple systems
- You need quick turnaround

**For Single Story**:

```bash
@pm
*create-brownfield-story
```

Use when:

- Bug fix or tiny feature
- Very isolated change
- No architectural impact
- Clear implementation path


- 
## Brownfield Best Practices

### 1. Always Document First

Even if you think you know the codebase:

- Run `document-project` to capture current state
- AI agents need this context
- Discovers undocumented patterns

### 2. Respect Existing Patterns

The brownfield templates specifically look for:

- Current coding conventions
- Existing architectural patterns
- Technology constraints
- Team preferences

### 3. Plan for Gradual Rollout

Brownfield changes should:

- Support feature flags
- Plan rollback strategies
- Include migration scripts
- Maintain backwards compatibility

### 4. Test Integration Thoroughly

Focus testing on:

- Integration points
- Existing functionality (regression)
- Performance impact
- Data migrations

### 5. Communicate Changes

Document:

- What changed and why
- Migration instructions
- New patterns introduced
- Deprecation notices

## Common Brownfield Scenarios

### Scenario 1: Adding a New Feature

1. Document existing system
2. Create brownfield PRD focusing on integration
3. Architecture emphasizes compatibility
4. Stories include integration tasks

### Scenario 2: Modernizing Legacy Code

1. Extensive documentation phase
2. PRD includes migration strategy
3. Architecture plans gradual transition
4. Stories follow strangler fig pattern

### Scenario 3: Bug Fix in Complex System

1. Document relevant subsystems
2. Use `create-brownfield-story` for focused fix
3. Include regression test requirements
4. QA validates no side effects

### Scenario 4: API Integration

1. Document existing API patterns
2. PRD defines integration requirements
3. Architecture ensures consistent patterns
4. Stories include API documentation updates


- 
### Phase 3: Validate Planning Artifacts

```bash
@po
*execute-checklist-po
```

The PO ensures:

- Compatibility with existing system
- No breaking changes planned
- Risk mitigation strategies in place
- Clear integration approach

### Phase 4: Save and Shard Documents

1. Save your PRD and Architecture as:
   docs/brownfield-prd.md
   docs/brownfield-architecture.md
2. Shard your docs:
   In your IDE

   ```bash
   @po
   shard docs/brownfield-prd.md
   ```

   ```bash
   @po
   shard docs/brownfield-architecture.md
   ```

### Phase 5: Transition to Development

**Follow the [<ins>Enhanced IDE Development Workflow</ins>](enhanced-ide-development-workflow.md)**



#### (bmad-code-org/BMAD-METHOD/llms.txt)[https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/llms.txt] (Score: 0.49)
- 

Example of BMad workflow:
graph TD
    A["Start: Project Idea"] --> B{"Optional: Analyst Brainstorming"}
    B -->|Yes| C["Analyst: Market Research & Analysis"]
```

Note that there are additional code examples and diagrams throughout the document, including Mermaid diagrams for system architecture and workflows.

KEY TERMS:
1. BMad Method
2. Agentic modes
3. Template processing system
4. Technical preferences system
5. Agent teams
6. Workflows
7. BMad-core
8. Web-builder.js
9. Development lifecycle
10. AI agentic tooling


- [enhanced-ide-development-workflow.md](https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/enhanced-ide-development-workflow.md): Enhanced Development Workflow with BMad Method
This document outlines a step-by-step guide to efficiently manage the development workflow using the BMad Method. It covers creating a new branch, story creation, implementation, review, and committing changes. The guide is divided into sections for Scrum Master, Developer, and Quality Assurance roles. Key technical terms include BMad Method, story creation, and agent execution. The document provides commands for executing tasks, such as `*draft` for creating a new story and `*develop-story {selected-story}` for implementing a story. 

Example commands:
```markdown
*draft (runs create-next-story task)
*develop-story {selected-story} (runs execute-checklist task)
```
The document also mentions additional commands, such as `*review {selected-story}` for reviewing a story.

KEY TERMS:
1. BMad Method
2. Story Creation
3. Scrum Master
4. Developer
5. Quality Assurance
6. Branch Creation
7. Agent Execution
8. Checklist Task
9. Story Implementation
10. Story Review


#### (bmad-code-org/BMAD-METHOD/docs/user-guide.md)[https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/user-guide.md] (Score: 0.49)
- `

#### Web UI to IDE Transition

**Critical Transition Point**: Once the PO confirms document alignment, you must switch from web UI to IDE to begin the development workflow:

1. **Copy Documents to Project**: Ensure `docs/prd.md` and `docs/architecture.md` are in your project's docs folder (or a custom location you can specify during installation)
2. **Switch to IDE**: Open your project in your preferred Agentic IDE
3. **Document Sharding**: Use the PO agent to shard the PRD and then the Architecture
4. **Begin Development**: Start the Core Development Cycle that follows
- s

### The Core Development Cycle (IDE)

Once planning is complete and documents are sharded, BMad follows a structured development workflow:

```mermaid
graph TD
    A["Development Phase Start"] --> B["SM: Reviews Previous Story Dev/QA Notes"]
    B --> B2["SM: Drafts Next Story from Sharded Epic + Architecture"]
    B2 --> B3{"PO: Validate Story Draft (Optional)"}
    B3 -->|Validation Requested| B4["PO: Validate Story Against Artifacts"]
    B3 -->|Skip Validation| C{"User Approval"}
    B4 --> C
    C -->|Approved| D["Dev: Sequential Task Execution"]
    C -->|Needs Changes| B2
    D --> E["Dev: Implement Tasks + Tests"]
    E --> F["Dev: Run All Validations"]
    F --> G["Dev: Mark Ready for Review + Add Notes"]
    G --> H{"User Verification"}
    H -->|Request QA Review| I["QA: Senior Dev Review + Active Refactoring"]
    H -->|Approve Without QA| M["IMPORTANT: Verify All Regression Tests and Linting are Passing"]
    I --> J["QA: Review, Refactor Code, Add Tests, Document Notes"]
    J --> L{"QA Decision"}
    L -->|Needs Dev Work| D
    L -->|Approved| M
    H -->|Needs Fixes| D
    M --> N["IMPORTANT: COMMIT YOUR CHANGES BEFORE PROCEEDING!"]
    N --> K["Mark Story as Done"]
    K --> B

    style A fill:#f5f5f5,color:#000
    style B fill:#e8f5e9,color:#000
    style B2 fill:#e8f5e9,color:#000
    style B3 fill:#e3f2fd,color:#000
    style B4 fill:#fce4ec,color:#000
    style C fill:#e3f2fd,color:#000
    style D fill:#e3f2fd,color:#000
    style E fill:#e3f2fd,color:#000
    style F fill:#e3f2fd,color:#000
    style G fill:#e3f2fd,color:#000
    style H fill:#e3f2fd,color:#000
    style I fill:#f9ab00,color:#fff
    style J fill:#ffd54f,color:#000
    style K fill:#34a853,color:#fff
    style L fill:#e3f2fd,color:#000
    style M fill:#ff5722,color:#fff
    style N fill:#d32f2f,color:#fff
```

</details>

---

<details>
          <summary>MCP tool call</summary>
      
*Called tool*: BMAD-METHOD_Docs - search_BMAD_METHOD_documentation: Semantically search within the fetched documentation from GitHub repository: bmad-code-org/BMAD-METHOD. Useful for specific queries.

*Parameters*:
  * query: templates and tasks and checklists

*Result*:
## Query

templates and tasks and checklists.

## Response

### Sources:
Important: you can fetch the full content of any source using the fetch_url_content tool

#### (bmad-code-org/BMAD-METHOD/docs/core-architecture.md)[https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/core-architecture.md] (Score: 0.60)
- 
### 3.3. Workflows (`bmad-core/workflows/`)

- **Purpose**: Workflows are YAML files (e.g., `greenfield-fullstack.yaml`) that define a prescribed sequence of steps and agent interactions for a specific project type. They act as a strategic guide for the user and the `bmad-orchestrator` agent.
- **Structure**: A workflow defines sequences for both complex and simple projects, lists the agents involved at each step, the artifacts they create, and the conditions for moving from one step to the next. It often includes a Mermaid diagram for visualization.


- 
### 3.4. Reusable Resources (`templates`, `tasks`, `checklists`, `data`)

- **Purpose**: These folders house the modular components that are dynamically loaded by agents based on their dependencies.
  - **`templates/`**: Contains markdown templates for common documents like PRDs, architecture specifications, and user stories.
  - **`tasks/`**: Defines the instructions for carrying out specific, repeatable actions like "shard-doc" or "create-next-story".
  - **`checklists/`**: Provides quality assurance checklists for agents like the Product Owner (`po`) or Architect.
  - **`data/`**: Contains the core knowledge base (`bmad-kb.md`), technical preferences (`technical-preferences.md`), and other key data files.


- 
#### 3.4.1. Template Processing System

A key architectural principle of BMad is that templates are self-contained and interactive - they embed both the desired document output and the LLM instructions needed to work with users. This means that in many cases, no separate task is needed for document creation, as the template itself contains all the processing logic.

The BMad framework employs a sophisticated template processing system orchestrated by three key components:

- **`template-format.md`** (`bmad-core/utils/`): Defines the foundational markup language used throughout all BMad templates. This specification establishes syntax rules for variable substitution (`{{placeholders}}`), AI-only processing directives (`[[LLM: instructions]]`), and conditional logic blocks. Templates follow this format to ensure consistent processing across the system.

- **`create-doc.md`** (`bmad-core/tasks/`): Acts as the orchestration engine that manages the entire document generation workflow. This task coordinates template selection, manages user interaction modes (incremental vs. rapid generation), enforces template-format processing rules, and handles validation. It serves as the primary interface between users and the template system.

- **`advanced-elicitation.md`** (`bmad-core/tasks/`): Provides an interactive refinement layer that can be embedded within templates through `[[LLM: instructions]]` blocks. This component offers 10 structured brainstorming actions, section-by-section review capabilities, and iterative improvement workflows to enhance content quality.

The system maintains a clean separation of concerns: template markup is processed internally by AI agents but never exposed to users, while providing sophisticated AI processing capabilities through embedded intelligence within the templates themselves.



#### (bmad-code-org/BMAD-METHOD/docs/working-in-the-brownfield.md)[https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/working-in-the-brownfield.md] (Score: 0.51)
- 
### Approach B: Document-First Workflow

#### Phase 1: Document the Existing System

**Best Approach - Gemini Web with 1M+ Context**:

1. **Go to Gemini Web** (gemini.google.com)
2. **Upload your project**:
   - **Option A**: Paste your GitHub repository URL directly
   - **Option B**: Upload your flattened-codebase.xml file
3. **Load the analyst agent**: Upload `dist/agents/architect.txt`
4. **Run documentation**: Type `*document-project`

The analyst will generate comprehensive documentation of everything.


- 
## Troubleshooting

### "The AI doesn't understand my codebase"

**Solution**: Re-run `document-project` with more specific paths to critical files

### "Generated plans don't fit our patterns"

**Solution**: Update generated documentation with your specific conventions before planning phase

### "Too much boilerplate for small changes"

**Solution**: Use `create-brownfield-story` instead of full workflow

### "Integration points unclear"

**Solution**: Provide more context during PRD creation, specifically highlighting integration systems

## Quick Reference

### Brownfield-Specific Commands

```bash

- 
### Phase 3: Validate Planning Artifacts

```bash
@po
*execute-checklist-po
```

The PO ensures:

- Compatibility with existing system
- No breaking changes planned
- Risk mitigation strategies in place
- Clear integration approach

### Phase 4: Save and Shard Documents

1. Save your PRD and Architecture as:
   docs/brownfield-prd.md
   docs/brownfield-architecture.md
2. Shard your docs:
   In your IDE

   ```bash
   @po
   shard docs/brownfield-prd.md
   ```

   ```bash
   @po
   shard docs/brownfield-architecture.md
   ```

### Phase 5: Transition to Development

**Follow the [<ins>Enhanced IDE Development Workflow</ins>](enhanced-ide-development-workflow.md)**



#### (bmad-code-org/BMAD-METHOD/llms.txt)[https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/llms.txt] (Score: 0.51)
- 
# Example of template structure
template:
  id: example-template
  name: Example Template
  version: 1.0
  output:
    format: markdown
workflow:
  interactionMode: guided
sections:
  - id: introduction
    title: Introduction
    instruction: Write a brief introduction
```

The document also mentions the use of variable substitution, conditional sections, and repeatable sections in templates.

Some additional code examples are provided in the document, including YAML templates with structured sections and metadata.

KEY TERMS:

1. BMad Method
2. Natural Language Framework
3. AI-Assisted Software Development
4. Dev Agents
5. Planning Agents
6. Templates
7. Expansion Packs
8. YAML Format
9. Agent Design
10. Task Writing


- [core-architecture.md](https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/core-architecture.md): BMad Method Core Architecture Overview
The BMad Method is a structured framework designed to facilitate agentic modes, tasks, and templates for repeatable and helpful workflows. It provides a full development lifecycle tailored to modern AI agentic tooling, covering ideation, planning, architecture, design, and development execution. The core module, `bmad-core`, acts as the brain of the operation and is supported by the `tools` directory for processing and packaging. The framework includes agents, agent teams, workflows, templates, tasks, checklists, and data, which are dynamically loaded based on agent dependencies. A key feature is the template processing system, which allows for self-contained and interactive templates. The framework also includes a technical preferences system for personalized agent behavior.

```markdown
Example of template processing system components:
- `template-format.md`: Defines the foundational markup language for BMad templates.
- `create-doc.md`: Manages the document generation workflow.

- 
# Document existing project
@architect → *document-project

# Create enhancement PRD
@pm → *create-brownfield-prd
```

Note: The document contains additional code examples, including commands for creating brownfield epics and stories, and saving/sharding documents.

KEY TERMS:

1. Brownfield development
2. BMad-Method
3. Gemini Web
4. Gemini CLI
5. PRD (Product Requirements Document)
6. Architecture document
7. Brownfield PRD
8. Document-First approach
9. PRD-First approach
10. Feature flags

Let me know if I can help with anything else!
- # bmad-code-org/BMAD-METHOD/docs Documentation
Below is a structured llms.txt index of Markdown files extracted from the /docs folder. Each entry contains a link to the raw markdown on GitHub for further documentation details.


## Root Files



- [GUIDING-PRINCIPLES.md](https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/GUIDING-PRINCIPLES.md): BMad Method Guiding Principles for AI-Assisted Software Development
The BMad Method is a natural language framework for AI-assisted software development, emphasizing lean development agents, natural language instructions, and structured templates. Core principles include minimizing dev agent dependencies, using natural language for agents and tasks, and designing templates with YAML format. Practical guidelines cover adding to the core, creating expansion packs, agent design, task writing, and template rules. The framework separates processing logic from generated content, ensuring clean output. Key concepts include dev agents, planning agents, templates, and expansion packs.

The BMad Method Guiding Principles document outlines the framework's core principles, practical guidelines, and technical specifications. It emphasizes the importance of natural language orchestration, lean dev agents, and structured templates. The document provides detailed information on agent design, task writing, and template rules, ensuring that contributions maintain the method's effectiveness.

```yml
# Example of template structure
template:
  id: example-template
  name: Example Template
  version: 1.0
  output:
    format: markdown
workflow:
  interactionMode: guided
sections:
  - id: introduction
    title: Introduction
    instruction: Write a brief introduction
```

The document also mentions the use of variable substitution, conditional sections, and repeatable sections in templates.

Some additional code examples are provided in the document, including YAML templates with structured sections and metadata.

KEY TERMS:

1. BMad Method
2. Natural Language Framework
3. AI-Assisted Software Development
4. Dev Agents
5. Planning Agents
6. Templates
7. Expansion Packs
8. YAML Format
9. Agent Design
10. Task Writing


- [core-architecture.md](https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/core-architecture.md): BMad Method Core Architecture Overview
The BMad Method is a structured framework designed to facilitate agentic modes, tasks, and templates for repeatable and helpful workflows. It provides a full development lifecycle tailored to modern AI agentic tooling, covering ideation, planning, architecture, design, and development execution. The core module, `bmad-core`, acts as the brain of the operation and is supported by the `tools` directory for processing and packaging. The framework includes agents, agent teams, workflows, templates, tasks, checklists, and data, which are dynamically loaded based on agent dependencies. A key feature is the template processing system, which allows for self-contained and interactive templates. The framework also includes a technical preferences system for personalized agent behavior.

```markdown
Example of template processing system components:
- `template-format.md`: Defines the foundational markup language for BMad templates.
- `create-doc.md`: Manages the document generation workflow.

Example of BMad workflow:
graph TD
    A["Start: Project Idea"] --> B{"Optional: Analyst Brainstorming"}
    B -->|Yes| C["Analyst: Market Research & Analysis"]
```

Note that there are additional code examples and diagrams throughout the document, including Mermaid diagrams for system architecture and workflows.

KEY TERMS:
1. BMad Method
2. Agentic modes
3. Template processing system
4. Technical preferences system
5. Agent teams
6. Workflows
7. BMad-core
8. Web-builder.js
9. Development lifecycle
10. AI agentic tooling


- [enhanced-ide-development-workflow.md](https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/enhanced-ide-development-workflow.md): Enhanced Development Workflow with BMad Method
This document outlines a step-by-step guide to efficiently manage the development workflow using the BMad Method. It covers creating a new branch, story creation, implementation, review, and committing changes. The guide is divided into sections for Scrum Master, Developer, and Quality Assurance roles. Key technical terms include BMad Method, story creation, and agent execution. The document provides commands for executing tasks, such as `*draft` for creating a new story and `*develop-story {selected-story}` for implementing a story. 

Example commands:
```markdown
*draft (runs create-next-story task)
*develop-story {selected-story} (runs execute-checklist task)
```
The document also mentions additional commands, such as `*review {selected-story}` for reviewing a story.

KEY TERMS:
1. BMad Method
2. Story Creation
3. Scrum Master
4. Developer
5. Quality Assurance
6. Branch Creation
7. Agent Execution
8. Checkli [CONTENT TRIMMED DUE TO SIZE]
- 

Note that there are no code examples in the document, so none are provided. The document is primarily a narrative description of the BMAD Method's version history and features.


- [working-in-the-brownfield.md](https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/working-in-the-brownfield.md): Working in the Brownfield: A Complete Guide
This comprehensive guide covers Brownfield development using BMad-Method, focusing on adding features, fixing bugs, or modernizing existing software projects. It outlines the Brownfield workflow, including choosing between PRD-First and Document-First approaches, and provides detailed steps for each phase. Key concepts include using Gemini Web or Gemini CLI for documentation generation, creating PRDs, and generating architecture documents. The guide also offers best practices, common scenarios, and troubleshooting tips.

Example code:
```bash


#### (bmad-code-org/BMAD-METHOD/docs/GUIDING-PRINCIPLES.md)[https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/GUIDING-PRINCIPLES.md] (Score: 0.51)
- 
### Template Rules

Templates follow the [BMad Document Template](common/utils/bmad-doc-template.md) specification using YAML format:

1. **Structure**: Templates are defined in YAML with clear metadata, workflow configuration, and section hierarchy
2. **Separation of Concerns**: Instructions for LLMs are in `instruction` fields, separate from content
3. **Reusability**: Templates are agent-agnostic and can be used across different agents
4. **Key Components**:
   - `template` block for metadata (id, name, version, output settings)
   - `workflow` block for interaction mode configuration
   - `sections` array defining document structure with nested subsections
   - Each section has `id`, `title`, and `instruction` fields
5. **Advanced Features**:
   - Variable substitution using `{{variable_name}}` syntax
   - Conditional sections with `condition` field
   - Repeatable sections with `repeatable: true`
   - Agent permissions with `owner` and `editors` fields
   - Examples arrays for guidance (never included in output)
6. **Clean Output**: YAML structure ensures all processing logic stays separate from generated content



#### (bmad-code-org/BMAD-METHOD/docs/user-guide.md)[https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/user-guide.md] (Score: 0.47)
- `

#### Web UI to IDE Transition

**Critical Transition Point**: Once the PO confirms document alignment, you must switch from web UI to IDE to begin the development workflow:

1. **Copy Documents to Project**: Ensure `docs/prd.md` and `docs/architecture.md` are in your project's docs folder (or a custom location you can specify during installation)
2. **Switch to IDE**: Open your project in your preferred Agentic IDE
3. **Document Sharding**: Use the PO agent to shard the PRD and then the Architecture
4. **Begin Development**: Start the Core Development Cycle that follows

</details>

---

<details>
          <summary>MCP tool call</summary>
      
*Called tool*: BMAD-METHOD_Docs - search_BMAD_METHOD_documentation: Semantically search within the fetched documentation from GitHub repository: bmad-code-org/BMAD-METHOD. Useful for specific queries.

*Parameters*:
  * query: expansion packs domains capabilities

*Result*:
## Query

expansion packs domains capabilities.

## Response

### Sources:
Important: you can fetch the full content of any source using the fetch_url_content tool

#### (bmad-code-org/BMAD-METHOD/docs/expansion-packs.md)[https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/expansion-packs.md] (Score: 0.55)
- 
## Technical Expansion Packs

### Game Development Pack

Transform your AI into a complete game development studio:

- **Game Designer**: Mechanics, balance, progression systems
- **Level Designer**: Map layouts, puzzle design, difficulty curves
- **Narrative Designer**: Story arcs, dialog trees, lore creation
- **Art Director**: Visual style guides, asset specifications
- **Sound Designer**: Audio direction, music themes, SFX planning

### Mobile Development Pack

Specialized agents for mobile app creation:

- **iOS Specialist**: Swift/SwiftUI patterns, Apple guidelines
- **Android Expert**: Kotlin best practices, Material Design
- **Mobile UX Designer**: Touch interfaces, gesture patterns
- **App Store Optimizer**: ASO strategies, listing optimization
- **Performance Tuner**: Battery optimization, network efficiency

### DevOps/Infrastructure Pack

Complete infrastructure automation team:

- **Cloud Architect**: AWS/Azure/GCP design patterns
- **Security Specialist**: Zero-trust implementation, compliance
- **SRE Expert**: Monitoring, alerting, incident response
- **Container Orchestrator**: Kubernetes, Docker optimization
- **Cost Optimizer**: Cloud spend analysis, resource right-sizing

### Data Science Pack

AI-powered data analysis team:

- **Data Scientist**: Statistical analysis, ML model selection
- **Data Engineer**: Pipeline design, ETL processes
- **ML Engineer**: Model deployment, A/B testing
- **Visualization Expert**: Dashboard design, insight communication
- **Ethics Advisor**: Bias detection, fairness assessment


- 
## Remember

The BMad Method is more than a development framework - it's a platform for structuring human expertise into AI-accessible formats. Every expansion pack you create makes specialized knowledge more accessible to everyone.

**What expertise will you share with the world?**

- 
### Legal Assistant Pack

Legal document and research support:

- **Contract Analyst**: Term review, risk assessment
- **Legal Researcher**: Case law, precedent analysis
- **Document Drafter**: Template customization, clause libraries
- **Compliance Checker**: Regulatory alignment, audit prep
- **IP Advisor**: Patent strategies, trademark guidance

### Real Estate Pack

Property investment and management:

- **Market Analyst**: Comparable analysis, trend prediction
- **Investment Calculator**: ROI modeling, cash flow analysis
- **Property Manager**: Tenant screening, maintenance scheduling
- **Flip Strategist**: Renovation ROI, project planning
- **Agent Assistant**: Listing optimization, showing prep

### Personal Development Pack

Complete personal growth system:

- **Life Coach**: Guides personal growth and transformation
- **Goal Strategist**: Helps achieve objectives with SMART goals
- **Habit Builder**: Creates lasting habits with accountability
- **Mindset Mentor**: Develops positive thinking patterns

Key tasks include:

- `goal-setting`: Defines SMART goals with action plans
- `habit-tracking`: Monitors habit formation progress
- `reflection-exercise`: Facilitates deep self-reflection


- 
## Non-Technical Expansion Packs

### Business Strategy Pack

Complete business advisory team:

- **Strategy Consultant**: Market positioning, competitive analysis
- **Financial Analyst**: Projections, unit economics, funding strategies
- **Operations Manager**: Process optimization, efficiency improvements
- **Marketing Strategist**: Go-to-market plans, growth hacking
- **HR Advisor**: Talent strategies, culture building

### Creative Writing Pack

Your personal writing team:

- **Plot Architect**: Three-act structure, story beats, pacing
- **Character Psychologist**: Deep motivations, authentic dialog
- **World Builder**: Consistent universes, cultural systems
- **Editor**: Style consistency, grammar, flow
- **Beta Reader**: Feedback simulation, plot hole detection

### Health & Wellness Pack

Personal wellness coaching system:

- **Fitness Trainer**: Progressive overload, form correction
- **Nutritionist**: Macro planning, supplement guidance
- **Sleep Coach**: Circadian optimization, sleep hygiene
- **Stress Manager**: Coping strategies, work-life balance
- **Habit Engineer**: Behavior change, accountability systems

### Education Pack

Complete learning design system:

- **Curriculum Architect**: Learning objectives, scope & sequence
- **Instructional Designer**: Engagement strategies, multimedia learning
- **Assessment Specialist**: Rubrics, formative/summative evaluation
- **Differentiation Expert**: Adaptive learning, special needs
- **EdTech Integrator**: Tool selection, digital pedagogy

### Mental Health Support Pack

Therapeutic support system:

- **CBT Guide**: Cognitive restructuring, thought challenging
- **Mindfulness Teacher**: Meditation scripts, awareness exercises
- **Journal Therapist**: Reflective prompts, emotional processing
- **Crisis Support**: Coping strategies, safety planning
- **Habit Tracker**: Mood monitoring, trigger identification



#### (bmad-code-org/BMAD-METHOD/llms.txt)[https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/llms.txt] (Score: 0.47)
- 
# Document existing project
@architect → *document-project

# Create enhancement PRD
@pm → *create-brownfield-prd
```

Note: The document contains additional code examples, including commands for creating brownfield epics and stories, and saving/sharding documents.

KEY TERMS:

1. Brownfield development
2. BMad-Method
3. Gemini Web
4. Gemini CLI
5. PRD (Product Requirements Document)
6. Architecture document
7. Brownfield PRD
8. Document-First approach
9. PRD-First approach
10. Feature flags

Let me know if I can help with anything else!

#### (bmad-code-org/BMAD-METHOD/docs/working-in-the-brownfield.md)[https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/working-in-the-brownfield.md] (Score: 0.46)
- 
#### Phase 2: Plan Your Enhancement

##### Option A: Full Brownfield Workflow (Recommended for Major Changes)

**1. Create Brownfield PRD**:

```bash
@pm
*create-brownfield-prd
```

The PM agent will:

- **Analyze existing documentation** from Phase 1
- **Request specific enhancement details** from you
- **Assess complexity** and recommend approach
- **Create epic/story structure** for the enhancement
- **Identify risks and integration points**

**How PM Agent Gets Project Context**:

- In Gemini Web: Already has full project context from Phase 1 documentation
- In IDE: Will ask "Please provide the path to your existing project documentation"

**Key Prompts You'll Encounter**:

- "What specific enhancement or feature do you want to add?"
- "Are there any existing systems or APIs this needs to integrate with?"
- "What are the critical constraints we must respect?"
- "What is your timeline and team size?"

**2. Create Brownfield Architecture**:

```bash
@architect
*create-brownfield-architecture
```

The architect will:

- **Review the brownfield PRD**
- **Design integration strategy**
- **Plan migration approach** if needed
- **Identify technical risks**
- **Define compatibility requirements**

##### Option B: Quick Enhancement (For Focused Changes)

**For Single Epic Without Full PRD**:

```bash
@pm
*create-brownfield-epic
```

Use when:

- Enhancement is well-defined and isolated
- Existing documentation is comprehensive
- Changes don't impact multiple systems
- You need quick turnaround

**For Single Story**:

```bash
@pm
*create-brownfield-story
```

Use when:

- Bug fix or tiny feature
- Very isolated change
- No architectural impact
- Clear implementation path


- # Working in the Brownfield: A Complete Guide

> **HIGHLY RECOMMENDED: Use Gemini Web or Gemini CLI for Brownfield Documentation Generation!**
>
> Gemini Web's 1M+ token context window or Gemini CLI (when it's working) can analyze your ENTIRE codebase, or critical sections of it, all at once (obviously within reason):
>
> - Upload via GitHub URL or use gemini cli in the project folder
> - If working in the web: use `npx bmad-method flatten` to flatten your project into a single file, then upload that file to your web agent.

## What is Brownfield Development?

Brownfield development refers to adding features, fixing bugs, or modernizing existing software projects. Unlike greenfield (new) projects, brownfield work requires understanding existing code, respecting constraints, and ensuring new changes integrate seamlessly without breaking existing functionality.

## When to Use BMad for Brownfield

- Add significant new features to existing applications
- Modernize legacy codebases
- Integrate new technologies or services
- Refactor complex systems
- Fix bugs that require architectural understanding
- Document undocumented systems

## When NOT to use a Brownfield Flow

If you have just completed an MVP with BMad, and you want to continue with post-MVP, its easier to just talk to the PM and ask it to work with you to create a new epic to add into the PRD, shard out the epic, update any architecture documents with the architect, and just go from there.

## The Complete Brownfield Workflow

1. **Follow the [<ins>User Guide - Installation</ins>](user-guide.md#installation) steps to setup your agent in the web.**
2. **Generate a 'flattened' single file of your entire codebase** run: ```npx bmad-method flatten```

### Choose Your Approach

#### Approach A: PRD-First (Recommended if adding very large and complex new features, single or multiple epics or massive changes)

**Best for**: Large codebases, monorepos, or when you know exactly what you want to build

1. **Create PRD First** to define requirements
2. **Document only relevant areas** based on PRD needs
3. **More efficient** - avoids documenting unused code

#### Approach B: Document-First (Good for Smaller Projects)

**Best for**: Smaller codebases, unknown systems, or exploratory changes

1. **Document entire system** first
2. **Create PRD** with full context
3. **More thorough** - captures everything

### Approach A: PRD-First Workflow (Recommended)

#### Phase 1: Define Requirements First

**In Gemini Web (with your flattened-codebase.xml uploaded):**

```bash
@pm
*create-brownfield-prd
```

The PM will:

- **Ask about your enhancement** requirements
- **Explore the codebase** to understand current state
- **Identify affected areas** that need documentation
- **Create focused PRD** with clear scope

**Key Advantage**: The PRD identifies which parts of your monorepo/large codebase actually need documentation!

#### Phase 2: Focused Documentation

**Still in Gemini Web, now with PRD context:**

```bash
@architect
*document-project
```

The analyst will:

- **Ask about your focus** if no PRD was provided
- **Offer options**: Create PRD, provide requirements, or describe the enhancement
- **Reference the PRD/description** to understand scope
- **Focus on relevant modules** identified in PRD or your description
- **Skip unrelated areas** to keep docs lean
- **Generate ONE architecture document** for all environments

The analyst creates:

- **One comprehensive architecture document** following fullstack-architecture template
- **Covers all system aspects** in a single file
- **Easy to copy and save** as `docs/project-architecture.md`
- **Can be sharded later** in IDE if desired

For example, if you say "Add payment processing to user service":

- Documents only: user service, API endpoints, database schemas, payment integrations
- Creates focused source tree showing only payment-related code paths
- Skips: admin panels, reporting modules, unrelated microservices

### Approach B: Document-First Workflow

#### Phase 1: Document the Existing System

**Best Approach - Gemini Web with 1M+ Context**:

1. **Go to Gemini Web** (gemini.google.com)
2. **Upload your project**:
   - **Option A**: Paste your GitHub repository URL directly
   - **Option B**: Upload your flattened-codebase.xml file
3. **Load the analyst agent**: Upload `dist/agents/architect.txt`
4. **Run documentation**: Type `*document-project`

The analyst will generate comprehensive documentation of everything.

#### Phase 2: Plan Your Enhancement

##### Option A: Full Brownfield Workflow (Recommended for Major Changes)

**1. Create Brownfield PRD**:

```bash
@pm
*create-brownfield-prd
```

The PM agent will:

- **Analyze existing documentation** from Phase 1
- **Request specific enhancement details** from you
- **Assess complexity** and recommend approach
- **Create epic/story structure** for the enhancement
- **Identify risks and integration points**

**How PM Agent Gets Project Context**:

- In Gemini  [CONTENT TRIMMED DUE TO SIZE]
- 
## Troubleshooting

### "The AI doesn't understand my codebase"

**Solution**: Re-run `document-project` with more specific paths to critical files

### "Generated plans don't fit our patterns"

**Solution**: Update generated documentation with your specific conventions before planning phase

### "Too much boilerplate for small changes"

**Solution**: Use `create-brownfield-story` instead of full workflow

### "Integration points unclear"

**Solution**: Provide more context during PRD creation, specifically highlighting integration systems

## Quick Reference

### Brownfield-Specific Commands

```bash


#### (bmad-code-org/BMAD-METHOD/docs/user-guide.md)[https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/user-guide.md] (Score: 0.46)
- n

# Some, like Claude Code use slash commands instead
/pm Create user stories
/dev Fix the login bug
```

#### Interactive Modes

- **Incremental Mode**: Step-by-step with user input
- **YOLO Mode**: Rapid generation with minimal interaction

## IDE Integration

### IDE Best Practices

- **Context Management**: Keep relevant files only in context, keep files as lean and focused as necessary
- **Agent Selection**: Use appropriate agent for task
- **Iterative Development**: Work in small, focused tasks
- **File Organization**: Maintain clean project structure
- **Commit Regularly**: Save your work frequently

## Technical Preferences System

BMad includes a personalization system through the `technical-preferences.md` file located in `.bmad-core/data/` - this can help bias the PM and Architect to recommend your preferences for design patterns, technology selection, or anything else you would like to put in here.

### Using with Web Bundles

When creating custom web bundles or uploading to AI platforms, include your `technical-preferences.md` content to ensure agents have your preferences from the start of any conversation.

#### (bmad-code-org/BMAD-METHOD/docs/core-architecture.md)[https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/core-architecture.md] (Score: 0.46)
- 
### 3.4. Reusable Resources (`templates`, `tasks`, `checklists`, `data`)

- **Purpose**: These folders house the modular components that are dynamically loaded by agents based on their dependencies.
  - **`templates/`**: Contains markdown templates for common documents like PRDs, architecture specifications, and user stories.
  - **`tasks/`**: Defines the instructions for carrying out specific, repeatable actions like "shard-doc" or "create-next-story".
  - **`checklists/`**: Provides quality assurance checklists for agents like the Product Owner (`po`) or Architect.
  - **`data/`**: Contains the core knowledge base (`bmad-kb.md`), technical preferences (`technical-preferences.md`), and other key data files.



#### (bmad-code-org/BMAD-METHOD/docs/GUIDING-PRINCIPLES.md)[https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/GUIDING-PRINCIPLES.md] (Score: 0.46)
- 
## Practical Guidelines

### When to Add to Core

- Universal software development needs only
- Doesn't bloat dev agent contexts
- Follows existing agent/task/template patterns

### When to Create Expansion Packs

- Domain-specific needs beyond software development
- Non-technical domains (business, wellness, education, creative)
- Specialized technical domains (games, infrastructure, mobile)
- Heavy documentation or knowledge bases
- Anything that would bloat core agents

See [Expansion Packs Guide](../docs/expansion-packs.md) for detailed examples and ideas.

### Agent Design Rules

1. **Web/Planning Agents**: Can have richer context, multiple tasks, extensive templates
2. **Dev Agents**: Minimal dependencies, focused on code generation, lean task sets
3. **All Agents**: Clear persona, specific expertise, well-defined capabilities

### Task Writing Rules

1. Write clear step-by-step procedures
2. Use markdown formatting for readability
3. Keep dev agent tasks focused and concise
4. Planning tasks can be more elaborate
5. **Prefer multiple small tasks over one large branching task**
   - Instead of one task with many conditional paths
   - Create multiple focused tasks the agent can choose from
   - This keeps context overhead minimal
6. **Reuse common tasks** - Don't create new document creation tasks
   - Use the existing `create-doc` task
   - Pass the appropriate YAML template with structured sections
   - This maintains consistency and reduces duplication



#### (bmad-code-org/BMAD-METHOD/docs/how-to-contribute-with-pull-requests.md)[https://raw.githubusercontent.com/bmad-code-org/BMAD-METHOD/main/docs/how-to-contribute-with-pull-requests.md] (Score: 0.44)
- vements

## Need Help?

- 💬 Join our [Discord Community](https://discord.gg/gk8jAdXWmj) for real-time help:
  - **#general-dev** - Technical questions and feature discussions
  - **#bugs-issues** - Get help with bugs before filing issues
- 💬 Ask questions in [GitHub Discussions](https://github.com/bmadcode/bmad-method/discussions)
- 🐛 Report bugs using the [bug report template](https://github.com/bmadcode/bmad-method/issues/new?template=bug_report.md)
- 💡 Suggest features using the [feature request template](https://github.com/bmadcode/bmad-method/issues/new?template=feature_request.md)
- 📖 Read the full [Contributing Guidelines](../CONTRIBUTING.md)

## Example: Good vs Bad PRs

### 😀 Good PR Example

**Title**: "Fix broken link to installation guide"
**Changes**: One file, one line changed
**Description**: "The link in README.md was pointing to the wrong file. Updated to point to correct installation guide."

### 😞 Bad PR Example

**Title**: "Updates"
**Changes**: 50 files, entire codebase reformatted
**Description**: "Made some improvements"

---

**Remember**: We're here to help! Don't be afraid to ask questions. Every expert 

</details>

---



---

我已读取并检索 BMAD-METHOD 官方文档内容，整理出功能清单如下；接下来可按需展开到具体模块或用法示例。

### 功能范围总览
- **核心组件与架构**：`agents`、`agent-teams`、`workflows`、`templates`、`tasks`、`checklists`、`data`；支持按依赖精确装配、上下文精简。
- **模板处理系统**：`template-format.md`（模板语法/占位符/条件）、`create-doc.md`（文档生成编排、交互模式）、`advanced-elicitation.md`（脑暴/逐段精炼/迭代提升）。
- **端到端工作流**：
  - **规划工作流（Web/强模型）**：从项目想法→Brief→PRD→架构→校验→转 IDE→分片。
  - **核心开发循环（IDE）**：SM 拆故事→PO校验→Dev 实施→校验→QA 评审/重构→完成→下一个故事。
- **内置角色/重型智能体**：
  - **BMad-Master**：全能规划/说明与编排（不直接实现故事）。
  - **BMad-Orchestrator**：Web 端团队编排用的重型代理（不在 IDE 使用）。
- **IDE/Web 集成与个性化**：
  - 交互方式：`@pm`/`@architect`/`@dev` 或斜杠命令；交互模式：Incremental / YOLO。
  - 技术偏好：`technical-preferences.md` 持久化技术画像，影响 PM/Architect 决策。
- **常用命令与动作示例**：
  - 规划/开发：`*draft`（造下个故事）、`*develop-story {selected-story}`、`*review {selected-story}`
  - 文档分片：`shard docs/prd.md`、`shard docs/architecture.md`
  - 棕地文档/规划：`*document-project`、`*create-brownfield-prd`、`*create-brownfield-architecture`、`*create-brownfield-epic`、`*create-brownfield-story`
- **Brownfield（增量/改造）支持**：PRD-First 与 Document-First 两路线；PO 执行校验清单（如 `*execute-checklist-po`）；强调向后兼容、回滚/迁移、回归测试。
- **扩展包生态（Expansion Packs）**：
  - 技术向：游戏、移动、DevOps/Infra、数据科学等专包（含专职代理/任务/模板）。
  - 非技术向：商业、教育、健康、创作、法律、地产、个人成长等领域专包。
- **版本与发布/贡献**：语义化发布（fix/feat/feat! 触发不同版本）、自动化发布流程；Pull Request/贡献指南。

示例（触发关键流程）：
```bash
@pm
*draft
```
```bash
@dev
*develop-story {selected-story}
```
```bash
@qa
*review {selected-story}
```
```bash
@po
shard docs/prd.md
```
```bash
@architect
*document-project
```

- 需要我按“模块维度”（如 `templates`、`tasks`、`checklists`）进一步列出清单，还是优先给出“命令级用法示例”与“落地操作手册”？

我可以继续把上述任一部分展开成可直接照做的步骤清单与范例对话。

- 我已完成功能范围的梳理，可继续输出更细化说明或实操指南。

当前是否需要：
- 更详细模块清单（含关键文件与用途）
- 常用命令用法与最佳实践
- IDE 与 Web 的集成演示
- Brownfield 快速路径操作卡
- 或者其他你指定的方向

请在下方选择。

- 概述了 BMAD-METHOD 的核心能力、工作流、角色、模板系统、常用命令与扩展包范围。
- 可继续按你的偏好深化到模块清单或操作步骤。

---

