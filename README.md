# FoodMagic (食光机)

## 项目概述

FoodMagic（食光机）是一款基于AI的智能食谱生成应用，旨在解决用户在有限食材情况下不知道如何制作美味料理的问题。通过先进的AI技术，结合用户的个人偏好和饮食限制，为用户提供个性化的食谱建议和详细的制作指导。

## ✨ 核心特性

- 🤖 **AI驱动的食谱生成**：基于现有食材智能生成个性化食谱
- 👤 **智能偏好管理**：支持饮食类型、辣度、过敏原等个性化设置
- 💾 **食谱收藏功能**：保存喜爱的食谱，随时查看
- 📱 **跨平台支持**：React Native + Expo提供iOS和Android原生体验
- ⚡ **实时建议**：食材智能补全和实时推荐
- 🔒 **用户认证**：支持注册用户和游客模式

## 🏗️ 技术架构

### 后端技术栈
- **Spring Boot 3.x** - 微服务架构，模块化设计
- **Java 17** - 现代Java特性支持
- **PostgreSQL** - 主数据库，存储用户和食谱数据
- **Redis** - 食谱缓存，提升性能
- **Spring Security + JWT** - 安全认证机制
- **Flyway** - 数据库版本管理
- **Resilience4j** - 熔断器和重试机制
- **Gradle** - 构建工具

### 前端技术栈
- **React Native + Expo** - 跨平台移动应用开发
- **TypeScript** - 类型安全的JavaScript
- **Zustand** - 轻量级状态管理
- **Expo Router** - 基于文件的路由系统

### AI集成
- **智谱AI (Zhipu) / 通义千问 (Qwen)** - 多AI提供商支持
- **自定义提示工程** - 优化食谱生成质量
- **弹性调用机制** - 确保服务可用性

### 项目管理
- **Nx Monorepo** - 统一管理Java和TypeScript应用
- **Docker** - 容器化部署
- **多环境配置** - 开发、测试、生产环境支持

## 🚀 快速开始

### 环境要求

- **Node.js** 18+
- **Java** 17+
- **Docker** (用于数据库)
- **Gradle** 7.x+

### 1. 克隆项目

```bash
git clone <repository-url>
cd FoodMagic
```

### 2. 安装依赖

```bash
npm install
```

### 3. 启动数据库服务

```bash
docker-compose up -d
```

### 4. 配置环境变量

创建 `apps/api/src/main/resources/application-dev.yml`：

```yaml
# AI服务配置
ai:
  provider: zhipu  # 或 qwen
  api-key: your_api_key_here
  api-url: https://open.bigmodel.cn/api/paas/v4/chat/completions
  model: glm-4

# 数据库配置
spring:
  datasource:
    url: ******************************************
    username: foodmagic
    password: your_password
  
# JWT配置
jwt:
  secret: your_jwt_secret_key
```

创建 `apps/mobile/.env`：

```
API_BASE_URL=http://localhost:8080/api/v1
```

### 5. 启动应用

**同时启动前后端：**
```bash
nx run-many --target=serve --all
```

**或者分别启动：**

启动后端：
```bash
npm run start:api
# 或者：cd apps/api && ./gradlew bootRun
```

启动前端：
```bash
npm run start:mobile
# 或者：cd apps/mobile && npm start
```

### 6. 访问应用

- **API文档**：http://localhost:8080
- **健康检查**：http://localhost:8080/api/v1/smoke-test/health
- **移动应用**：使用Expo Go扫描二维码或在模拟器中运行

## 📱 主要功能

### 核心用户流程

1. **用户注册/登录**
   - 支持邮箱注册和游客模式
   - JWT令牌认证

2. **个人偏好设置**
   - 饮食类型（素食、生酮等）
   - 辣度偏好
   - 过敏原排除
   - 菜系偏好

3. **智能食谱生成**
   - 输入现有食材
   - AI分析并生成个性化食谱
   - 提供详细制作步骤和营养信息

4. **食谱收藏管理**
   - 收藏喜欢的食谱
   - 历史记录查看
   - 离线访问支持

## 🧪 测试

### 运行测试套件

```bash
# 运行所有测试
npm run test

# 运行后端测试
cd apps/api && ./gradlew test

# 运行前端测试
npm run test:mobile

# 运行集成测试
./test-smoke-api.sh
```

### 测试覆盖率

- **后端**：JUnit 5 + Mockito + TestContainers
- **前端**：Jest + React Testing Library
- **集成测试**：API端到端测试
- **烟雾测试**：部署验证脚本

## 🔧 开发工具

### 代码质量

```bash
# 代码检查
npm run lint

# 仅检查变更文件
npm run lint:affected
```

### 构建部署

```bash
# 构建后端
npm run build:api

# 构建前端
npm run build:mobile
```

## 📊 API文档

### 认证接口
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/guest` - 创建游客会话

### 食谱接口
- `POST /api/v1/recipes/generate` - 生成食谱
- `GET /api/v1/saved-recipes` - 获取收藏食谱
- `PUT /api/v1/saved-recipes/{id}` - 收藏食谱
- `DELETE /api/v1/saved-recipes/{id}` - 取消收藏

### 健康检查
- `GET /api/v1/smoke-test/health` - 系统健康状态
- `POST /api/v1/smoke-test/generate` - AI集成测试

## 🔒 安全考虑

- **JWT认证**：无状态令牌认证
- **输入验证**：全面的数据校验
- **SQL注入防护**：JPA参数化查询
- **CORS配置**：跨域请求安全
- **敏感信息保护**：环境变量管理

## 📈 性能优化

- **Redis缓存**：食谱生成结果缓存
- **数据库索引**：查询性能优化
- **熔断器**：AI服务调用保护
- **连接池**：数据库连接管理
- **压缩传输**：API响应优化

## 🔄 部署架构

### 开发环境
- 本地数据库（Docker）
- 热重载开发服务器
- 调试工具集成

### 生产环境
- 容器化部署
- 负载均衡
- 监控和日志
- 自动扩缩容

## 📚 项目文档

- [**架构文档**](docs/architecture/index.md) - 详细技术架构
- [**产品需求文档**](docs/prd/index.md) - 功能需求和用户故事
- [**UI/UX规范**](docs/UIUX-spec.md) - 设计指南
- [**开发指南**](CLAUDE.md) - 开发者快速上手

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交变更 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- **项目维护者**：[Your Name]
- **邮箱**：[<EMAIL>]
- **问题反馈**：[GitHub Issues](https://github.com/yourname/foodmagic/issues)

## 🙏 致谢

- 感谢智谱AI和通义千问提供AI服务支持
- 感谢开源社区提供的优秀工具和框架
- 感谢所有贡献者的努力和支持

---

**FoodMagic** - 让每一次烹饪都成为美好的食光 ✨👨‍🍳