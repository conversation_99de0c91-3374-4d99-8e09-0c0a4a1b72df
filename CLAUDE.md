# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

FoodMagic (食光机) 是一款基于AI的智能食谱生成应用，采用模块化单体架构构建。后端使用Spring Boot，前端使用React Native + Expo，通过Nx Monorepo统一管理。

## 开发命令

### 快速启动
```bash
# 安装依赖
npm install

# 启动后端服务
npm run start:api
# 或直接运行: cd apps/api && ./gradlew bootRun

# 启动移动端应用
npm run start:mobile
# 或直接运行: cd apps/mobile && npm start

# 同时启动前后端
nx run-many --target=serve --all
```

### 构建
```bash
# 构建后端
npm run build:api
# 或: cd apps/api && ./gradlew build

# 构建移动端
npm run build:mobile
```

### 测试
```bash
# 运行所有测试
npm run test

# 运行单个测试文件 (后端)
cd apps/api && ./gradlew test --tests "com.foodmagic.user.service.UserPreferenceServiceTest"

# 运行单个测试类 (前端)
cd apps/mobile && npm test -- --testNamePattern="IngredientInput"

# 运行后端测试
cd apps/api && ./gradlew test

# 运行移动端测试
npm run test:mobile

# 运行受影响的测试（基于git变更）
npm run test:affected

# 运行烟雾测试脚本
./test-smoke-api.sh
```

### 代码质量
```bash
# 运行所有项目的代码检查
npm run lint

# 运行受影响项目的代码检查
npm run lint:affected
```

### Docker服务
```bash
# 启动PostgreSQL和Redis
docker-compose up -d

# 停止服务
docker-compose down
```

## 架构说明

### 项目结构
```
FoodMagic/
├── apps/                        # 主要应用
│   ├── api/                     # Spring Boot后端 (主要开发区域)
│   │   ├── src/main/java/com/foodmagic/
│   │   │   ├── auth/           # 认证模块：JWT认证、用户管理
│   │   │   ├── recipe/         # 食谱模块：AI生成、CRUD操作
│   │   │   ├── user/           # 用户模块：偏好管理
│   │   │   ├── ai/             # AI集成：PromptBuilder、AiServiceClient
│   │   │   └── common/         # 通用工具：安全、异常处理
│   │   └── src/main/resources/db/migration/ # Flyway数据库迁移
│   └── mobile/                  # React Native前端
│       ├── app/                 # Expo Router页面
│       ├── components/          # 可复用UI组件
│       ├── services/            # API服务层
│       └── stores/              # Zustand状态管理
├── docs/                        # 完整的项目文档
│   ├── architecture/            # 详细架构文档
│   ├── prd/                     # 产品需求文档
│   └── stories/                 # 开发用户故事
└── food-magic-app/             # 实际的Nx工作空间
    ├── apps/api/               # API应用实现
    ├── apps/mobile/            # 移动应用实现
    └── packages/               # 共享代码
```

### Monorepo结构
- **Nx Monorepo** 管理Java (Spring Boot) 和 TypeScript (React Native) 应用
- **apps/api**: Spring Boot后端应用 (Java 17)
- **apps/mobile**: React Native前端应用，使用Expo (TypeScript)
- **packages/**: 共享代码和配置

### 后端架构 (Spring Boot)

后端采用**模块化单体**架构，模块边界清晰：

1. **认证模块** (`com.foodmagic.auth`): 用户认证和JWT管理
   - Controller: 处理登录、注册、游客会话
   - Service: AuthService提供JWT令牌生成
   - Security: Spring Security配合JWT过滤器

2. **食谱模块** (`com.foodmagic.recipe`): 核心食谱生成功能
   - Controller: 食谱生成端点
   - Service: 与AI服务客户端集成
   - DTO: 食谱数据的请求/响应结构

3. **用户模块** (`com.foodmagic.user`): 用户偏好和档案管理
   - Service: PreferenceService用于用户定制
   - Repository: 用户数据的JPA存储库

4. **AI模块** (`com.foodmagic.ai`): AI集成抽象层
   - PromptBuilder: 构建AI提示，集成用户偏好
   - AiServiceClient: 处理与AI提供商的通信(智谱/千问)
   - Resilience4j: 断路器和重试机制

5. **通用模块** (`com.foodmagic.common`): 共享工具
   - Security: JWT工具和认证过滤器
   - Exception: 全局异常处理
   - Config: 安全和基础设施配置

### 前端架构 (React Native/Expo)

移动应用使用基于组件的架构：

1. **导航**: Expo Router配合基于标签的导航
2. **状态管理**: Zustand存储用于认证、食谱、偏好
3. **服务**: API服务层用于后端通信
4. **组件**: 可复用的UI组件，包含测试

### 关键模式

1. **仓库模式**: Spring Data JPA用于数据访问
2. **DTO模式**: API契约的独立数据传输对象
3. **服务层**: 业务逻辑与控制器分离
4. **全局异常处理**: 使用`@RestControllerAdvice`的集中化错误处理
5. **JWT认证**: Spring Security的无状态认证
6. **断路器**: Resilience4j用于外部服务调用
7. **缓存**: Redis用于食谱缓存

## 环境配置

### 后端 (.env 或 application.yml)
```yaml
# 必需的AI配置
AI_PROVIDER: zhipu  # 或 qwen
AI_API_KEY: your_api_key_here
AI_API_URL: https://open.bigmodel.cn/api/paas/v4/chat/completions
AI_MODEL: glm-4

# 数据库
DB_HOST: localhost
DB_PORT: 5432
DB_NAME: foodmagic
DB_USERNAME: foodmagic
DB_PASSWORD: your_password

# JWT
JWT_SECRET: your_jwt_secret
```

### 前端 (apps/mobile/.env)
```
API_BASE_URL=http://localhost:8080/api/v1
```

## 数据库架构

应用使用PostgreSQL和Flyway迁移：

1. **users**: 用户账户和认证信息
2. **user_preferences**: 用户饮食偏好 (JSON格式)
3. **recipes**: AI生成的食谱
4. **user_saved_recipes**: 收藏夹的多对多关系

迁移文件位置: `apps/api/src/main/resources/db/migration/`

## API端点

基础URL: `/api/v1`

### 认证
- `POST /auth/register` - 用户注册
- `POST /auth/login` - 用户登录
- `POST /auth/guest` - 创建游客会话
- `POST /auth/convert-guest` - 游客转为注册用户

### 用户偏好
- `GET /users/me/preferences` - 获取用户偏好
- `PUT /users/me/preferences` - 更新用户偏好

### 食谱
- `POST /recipes/generate` - 根据食材生成食谱
- `GET /saved-recipes` - 获取用户收藏的食谱
- `PUT /saved-recipes/{recipeId}` - 收藏食谱
- `DELETE /saved-recipes/{recipeId}` - 取消收藏

### 烟雾测试
- `GET /smoke-test/health` - 健康检查
- `POST /smoke-test/generate` - 测试AI集成

## 测试策略

### 后端测试
- **单元测试**: JUnit 5配合Mockito进行模拟
- **集成测试**: TestContainers用于数据库测试
- 测试文件遵循Maven结构，位于`src/test/java`

### 前端测试
- **组件测试**: Jest配合React Testing Library
- 测试文件与组件同位置 (`.test.tsx`)

### E2E测试
- 烟雾测试脚本: `test-smoke-api.sh`
- 核心工作流的手动测试清单

## 常见任务

### 添加新的API端点
1. 在适当模块创建DTO类
2. 添加带有合适注解的控制器方法
3. 实现服务逻辑
4. 根据需要添加存储库方法
5. 编写单元和集成测试

### 使用AI服务
- 所有AI调用都通过`AiServiceClient`
- 使用`PromptBuilder`构建提示，集成用户偏好
- 配置断路器以确保弹性
- 支持多个提供商(智谱、千问)

### 数据库变更
1. 在`V{number}__description.sql`中创建新的Flyway迁移
2. 更新实体类
3. 更新存储库接口
4. 运行应用以应用迁移

## 重要注意事项

1. **模块边界**: 后端模块仅通过定义的服务接口通信
2. **安全性**: 永不硬编码密钥；使用环境变量
3. **AI集成**: 所有AI调用必须通过AI服务客户端
4. **缓存**: 食谱生成结果在Redis中缓存
5. **错误处理**: 全局异常处理器提供一致的错误响应
6. **JWT令牌**: 访问令牌存于内存，刷新令牌存于安全存储

## 项目特定上下文

### 用户偏好系统
- 用户偏好存储在JSONB格式的`user_preferences`表中
- 支持过敏原、饮食类型、菜系偏好等
- AI生成食谱时自动考虑用户偏好约束
- 支持硬约束（过敏原）和软偏好（菜系）的区分

### AI集成架构
- 优先支持中国主流AI模型（智谱GLM、通义千问）
- 通过`PromptBuilder`统一构建AI指令
- 集成用户偏好到AI提示中
- 使用Resilience4j确保服务可用性

### 模块化单体设计
- 清晰的模块边界，为微服务演进做准备
- 严格的模块间通信规则，由CI/CD强制执行
- 共享类型定义在`packages/shared-types`中统一管理

### 开发工作流
- 基于Nx的affected命令，只处理变更影响的项目
- GitHub Actions CI/CD流水线自动运行代码检查和测试
- TestContainers确保集成测试使用真实数据库环境

## 双工作空间说明

项目存在两个工作空间结构：

1. **主目录** (`/FoodMagic/`): 包含文档、规划和历史开发内容
   - `docs/`: 完整的架构和产品文档
   - `apps/api/` 和 `apps/mobile/`: 早期开发版本

2. **Nx工作空间** (`/food-magic-app/`): 当前活跃的开发环境
   - 完整的Nx Monorepo配置
   - 生产就绪的代码结构
   - CI/CD集成

**开发时使用**: 总是在 `food-magic-app/` 目录中工作，这是当前的实际开发环境。

## 关键开发路径

### 后端开发
- **主要代码**: `food-magic-app/apps/api/src/main/java/com/foodmagic/`
- **测试代码**: `food-magic-app/apps/api/src/test/java/com/foodmagic/`
- **数据库迁移**: `food-magic-app/apps/api/src/main/resources/db/migration/`

### 前端开发  
- **页面组件**: `food-magic-app/apps/mobile/app/`
- **UI组件**: `food-magic-app/apps/mobile/components/`
- **状态管理**: `food-magic-app/apps/mobile/stores/`
- **API服务**: `food-magic-app/apps/mobile/services/`