# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

FoodMagic (食光机) is an AI-powered recipe generation application built with a modular monolithic architecture. It uses Spring Boot for the backend and React Native with Expo for the mobile frontend, managed in an Nx Monorepo structure.

## Development Commands

### Quick Start
```bash
# Install dependencies
npm install

# Start backend service
npm run start:api
# Or directly:
cd apps/api && ./gradlew bootRun

# Start mobile app
npm run start:mobile
# Or directly:
cd apps/mobile && npm start

# Start both frontend and backend
nx run-many --target=serve --all
```

### Building
```bash
# Build backend
npm run build:api
# Or: cd apps/api && ./gradlew build

# Build mobile app
npm run build:mobile
```

### Testing
```bash
# Run all tests
npm run test

# Run backend tests
cd apps/api && ./gradlew test

# Run mobile tests
npm run test:mobile

# Run affected tests (based on git changes)
npm run test:affected

# Run smoke test script
./test-smoke-api.sh
```

### Code Quality
```bash
# Run linting for all projects
npm run lint

# Run linting for affected projects
npm run lint:affected
```

### Docker Services
```bash
# Start PostgreSQL and Redis
docker-compose up -d

# Stop services
docker-compose down
```

## Architecture

### Monorepo Structure
- **Nx Monorepo** manages both Java (Spring Boot) and TypeScript (React Native) applications
- **apps/api**: Spring Boot backend application (Java 17)
- **apps/mobile**: React Native frontend with Expo (TypeScript)
- **packages/**: Shared code and configurations

### Backend Architecture (Spring Boot)

The backend follows a **modular monolithic** architecture with clear module boundaries:

1. **Auth Module** (`com.foodmagic.auth`): User authentication and JWT management
   - Controller: Handles login, registration, guest sessions
   - Service: AuthService with JWT token generation
   - Security: Spring Security with JWT filter

2. **Recipe Module** (`com.foodmagic.recipe`): Core recipe generation functionality
   - Controller: Recipe generation endpoints
   - Service: Integration with AI service client
   - DTO: Request/response structures for recipe data

3. **User Module** (`com.foodmagic.user`): User preferences and profile management
   - Service: PreferenceService for user customization
   - Repository: JPA repositories for user data

4. **AI Module** (`com.foodmagic.ai`): AI integration abstraction
   - PromptBuilder: Constructs AI prompts with user preferences
   - AiServiceClient: Handles communication with AI providers (Zhipu/Qwen)
   - Resilience4j: Circuit breaker and retry mechanisms

5. **Common Module** (`com.foodmagic.common`): Shared utilities
   - Security: JWT utilities and authentication filters
   - Exception: Global exception handling
   - Config: Security and infrastructure configuration

### Frontend Architecture (React Native/Expo)

The mobile app uses a component-based architecture:

1. **Navigation**: Expo Router with tab-based navigation
2. **State Management**: Zustand stores for auth, recipes, preferences
3. **Services**: API service layer for backend communication
4. **Components**: Reusable UI components with tests

### Key Patterns

1. **Repository Pattern**: Spring Data JPA for data access
2. **DTO Pattern**: Separate data transfer objects for API contracts
3. **Service Layer**: Business logic separated from controllers
4. **Global Exception Handling**: Centralized error handling with `@RestControllerAdvice`
5. **JWT Authentication**: Stateless authentication with Spring Security
6. **Circuit Breaker**: Resilience4j for external service calls
7. **Caching**: Redis for recipe caching

## Environment Configuration

### Backend (.env or application.yml)
```yaml
# Required AI configuration
AI_PROVIDER: zhipu  # or qwen
AI_API_KEY: your_api_key_here
AI_API_URL: https://open.bigmodel.cn/api/paas/v4/chat/completions
AI_MODEL: glm-4

# Database
DB_HOST: localhost
DB_PORT: 5432
DB_NAME: foodmagic
DB_USERNAME: foodmagic
DB_PASSWORD: your_password

# JWT
JWT_SECRET: your_jwt_secret
```

### Frontend (apps/mobile/.env)
```
API_BASE_URL=http://localhost:8080/api/v1
```

## Database Schema

The application uses PostgreSQL with Flyway migrations:

1. **users**: User accounts with authentication
2. **user_preferences**: User dietary preferences (JSON)
3. **recipes**: AI-generated recipes
4. **user_saved_recipes**: Many-to-many relationship for favorites

Migrations are located in: `apps/api/src/main/resources/db/migration/`

## API Endpoints

Base URL: `/api/v1`

### Authentication
- `POST /auth/register` - User registration
- `POST /auth/login` - User login
- `POST /auth/guest` - Create guest session
- `POST /auth/convert-guest` - Convert guest to registered user

### Recipes
- `POST /recipes/generate` - Generate recipe from ingredients
- `GET /saved-recipes` - Get user's saved recipes
- `PUT /saved-recipes/{recipeId}` - Save a recipe
- `DELETE /saved-recipes/{recipeId}` - Remove from saved

### Smoke Test
- `GET /smoke-test/health` - Health check
- `POST /smoke-test/generate` - Test AI integration

## Testing Strategy

### Backend Testing
- **Unit Tests**: JUnit 5 with Mockito for mocking
- **Integration Tests**: Test containers for database testing
- Test files follow Maven structure in `src/test/java`

### Frontend Testing
- **Component Tests**: Jest with React Testing Library
- Test files colocated with components (`.test.tsx`)

### E2E Testing
- Smoke test script: `test-smoke-api.sh`
- Manual testing checklist for core workflows

## Common Tasks

### Adding a New API Endpoint
1. Create DTO classes in appropriate module
2. Add controller method with proper annotations
3. Implement service logic
4. Add repository methods if needed
5. Write unit and integration tests

### Working with AI Service
- All AI calls go through `AiServiceClient`
- Prompts are built using `PromptBuilder`
- Circuit breaker configured for resilience
- Supports multiple providers (Zhipu, Qwen)

### Database Changes
1. Create new Flyway migration in `V{number}__description.sql`
2. Update entity classes
3. Update repository interfaces
4. Run application to apply migration

## Important Notes

1. **Module Boundaries**: Backend modules communicate only through defined service interfaces
2. **Security**: Never hardcode secrets; use environment variables
3. **AI Integration**: All AI calls must go through the AI service client
4. **Caching**: Recipe generation results are cached in Redis
5. **Error Handling**: Global exception handler provides consistent error responses
6. **JWT Tokens**: Access tokens in memory, refresh tokens in secure storage