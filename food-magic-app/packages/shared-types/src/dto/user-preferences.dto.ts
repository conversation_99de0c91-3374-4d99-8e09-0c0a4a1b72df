export type DietType = 'LOW_CARB' | 'HIGH_PROTEIN' | 'KETO' | 'PALEO' | 'MEDITERRANEAN' | 'VEGAN' | 'NONE';

export type CuisineType = 
  | 'CHINESE' 
  | 'ITALIAN' 
  | 'JAPANESE' 
  | 'MEXICAN'
  | 'INDIAN'
  | 'FRENCH'
  | 'THAI'
  | 'KOREAN'
  | 'SPANISH'
  | 'GREEK'
  | 'VIETNAMESE'
  | 'MIDDLE_EASTERN'
  | 'AMERICAN'
  | 'BRAZILIAN'
  | 'AFRICAN';

export interface UserPreferencesDto {
  allergies?: string[];
  isVegetarian?: boolean;
  diet?: DietType;
  preferredCuisines?: CuisineType[];
  unitSystem?: 'METRIC' | 'IMPERIAL';
  locale?: string;
}