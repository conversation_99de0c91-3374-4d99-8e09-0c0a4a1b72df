package com.foodmagic.recipe.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 食谱数据传输对象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RecipeDto {
    private String id;
    private String name;
    private String description;
    private List<String> ingredients;
    private List<String> steps;
    private String nutrition;
    private String tips;
    private Integer cookingTime; // 分钟
    private String difficulty;
    private Integer servings;
    
    // 偏好相关字段
    private String dietType;
    private Boolean isVegetarian;
    private List<String> cuisineTypes;
    
    // 元数据
    private Long createdAt;
    private Long updatedAt;
}