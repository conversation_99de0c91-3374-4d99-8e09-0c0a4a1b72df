package com.foodmagic.recipe.dto;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 生成食谱请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GenerateRecipeRequestDto {
    
    @NotEmpty(message = "食材列表不能为空")
    @Size(min = 1, max = 20, message = "食材数量应在1-20个之间")
    private List<String> ingredients;
    
    // 可选：允许客户端覆盖用户默认偏好
    private UserPreferencesOverrideDto preferencesOverride;
}