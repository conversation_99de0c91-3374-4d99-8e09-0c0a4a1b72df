package com.foodmagic.recipe.service;

import com.foodmagic.ai.client.PromptBuilder;
import com.foodmagic.ai.service.AiServiceClient;
import com.foodmagic.auth.dto.UserPreferencesDto;
import com.foodmagic.recipe.dto.RecipeDto;
import com.foodmagic.recipe.dto.GenerateRecipeRequestDto;
import com.foodmagic.user.service.PreferenceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * 食谱生成服务
 * 集成用户偏好并调用AI服务生成食谱
 */
@Service
public class RecipeService {
    
    private static final Logger logger = LoggerFactory.getLogger(RecipeService.class);
    
    @Autowired
    private AiServiceClient aiServiceClient;
    
    @Autowired
    private PreferenceService preferenceService;
    
    /**
     * 生成食谱
     * 集成用户偏好并使用缓存优化性能
     * 
     * @param request 食谱生成请求
     * @return 生成的食谱
     */
    @Cacheable(value = "recipes", key = "@preferenceService.generateRecipeCacheKey(#userId, #request.ingredients)")
    public RecipeDto generateRecipe(GenerateRecipeRequestDto request, UUID userId) {
        logger.info("Generating recipe for user: {} with ingredients: {}", userId, request.getIngredients());
        
        try {
            // 获取用户偏好设置
            UserPreferencesDto preferences = preferenceService.getUserPreferences(userId);
            logger.debug("Retrieved preferences for user {}: diet={}, cuisines={}, allergies={}", 
                userId, preferences.getDiet(), preferences.getPreferredCuisines(), preferences.getAllergies());
            
            // 构建增强的提示词
            String enhancedPrompt = PromptBuilder.buildEnhancedRecipePrompt(
                request.getIngredients(), 
                preferences
            );
            
            // 调用AI服务生成食谱
            String aiResponse = aiServiceClient.generateResponse(enhancedPrompt);
            logger.debug("AI response received for user {}", userId);
            
            // 解析AI响应并构建RecipeDto
            RecipeDto recipe = parseAiResponse(aiResponse, request.getIngredients(), preferences);
            
            logger.info("Successfully generated recipe for user {}: {}", userId, recipe.getName());
            return recipe;
            
        } catch (Exception e) {
            logger.error("Failed to generate recipe for user {}: {}", userId, e.getMessage(), e);
            throw new RuntimeException("食谱生成失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 为当前登录用户生成食谱
     * 
     * @param request 食谱生成请求
     * @return 生成的食谱
     */
    public RecipeDto generateRecipeForCurrentUser(GenerateRecipeRequestDto request) {
        UUID userId = getCurrentUserId();
        return generateRecipe(request, userId);
    }
    
    /**
     * 解析AI响应并构建RecipeDto
     * 
     * @param aiResponse AI响应文本
     * @param ingredients 食材列表
     * @param preferences 用户偏好
     * @return 食谱DTO
     */
    private RecipeDto parseAiResponse(String aiResponse, List<String> ingredients, UserPreferencesDto preferences) {
        RecipeDto recipe = new RecipeDto();
        
        // 简单解析AI响应
        // 在生产环境中，应该使用更复杂的解析逻辑或结构化输出
        String[] lines = aiResponse.split("\n");
        
        // 提取菜品名称
        String name = extractRecipeName(lines);
        recipe.setName(name != null ? name : "AI推荐食谱");
        
        // 设置描述
        recipe.setDescription(generateDescription(ingredients, preferences));
        
        // 设置食材
        recipe.setIngredients(ingredients);
        
        // 提取制作步骤
        List<String> steps = extractSteps(lines);
        recipe.setSteps(steps);
        
        // 提取营养信息
        String nutrition = extractNutrition(lines);
        recipe.setNutrition(nutrition);
        
        // 提取烹饪贴士
        String tips = extractTips(lines);
        recipe.setTips(tips);
        
        // 设置偏好相关标签
        recipe.setDietType(preferences.getDiet());
        recipe.setIsVegetarian(preferences.getIsVegetarian());
        recipe.setCuisineTypes(preferences.getPreferredCuisines());
        
        // 设置其他属性
        recipe.setCookingTime(estimateCookingTime(steps));
        recipe.setDifficulty(estimateDifficulty(steps));
        recipe.setServings(2); // 默认2人份
        
        return recipe;
    }
    
    /**
     * 从AI响应中提取菜品名称
     */
    private String extractRecipeName(String[] lines) {
        for (String line : lines) {
            if (line.contains("菜品名称") || line.contains("推荐") || line.contains("可以制作")) {
                // 提取菜品名称的逻辑
                String cleanedLine = line.replaceAll(".*[:：]", "").trim();
                if (!cleanedLine.isEmpty()) {
                    return cleanedLine;
                }
            }
        }
        return null;
    }
    
    /**
     * 生成食谱描述
     */
    private String generateDescription(List<String> ingredients, UserPreferencesDto preferences) {
        StringBuilder desc = new StringBuilder("使用");
        desc.append(String.join("、", ingredients));
        desc.append("制作的美味菜肴");
        
        if (preferences.getDiet() != null) {
            desc.append("，适合").append(preferences.getDiet()).append("饮食");
        }
        
        if (preferences.getPreferredCuisines() != null && !preferences.getPreferredCuisines().isEmpty()) {
            desc.append("，融合").append(String.join("、", preferences.getPreferredCuisines())).append("风味");
        }
        
        return desc.toString();
    }
    
    /**
     * 从AI响应中提取制作步骤
     */
    private List<String> extractSteps(String[] lines) {
        List<String> steps = new java.util.ArrayList<>();
        boolean inStepsSection = false;
        
        for (String line : lines) {
            if (line.contains("制作步骤") || line.contains("步骤")) {
                inStepsSection = true;
                continue;
            }
            
            if (inStepsSection && !line.trim().isEmpty()) {
                // 检查是否是其他章节的开始
                if (line.contains("营养") || line.contains("贴士") || line.contains("价值")) {
                    inStepsSection = false;
                    continue;
                }
                
                // 清理步骤文本
                String step = line.replaceAll("^[0-9]+[.、]\\s*", "").trim();
                if (!step.isEmpty()) {
                    steps.add(step);
                }
            }
        }
        
        // 如果没有找到步骤，返回默认步骤
        if (steps.isEmpty()) {
            steps.add("准备所有食材");
            steps.add("按照食谱说明进行烹饪");
            steps.add("调味并装盘");
        }
        
        return steps;
    }
    
    /**
     * 从AI响应中提取营养信息
     */
    private String extractNutrition(String[] lines) {
        StringBuilder nutrition = new StringBuilder();
        boolean inNutritionSection = false;
        
        for (String line : lines) {
            if (line.contains("营养") || line.contains("价值")) {
                inNutritionSection = true;
                continue;
            }
            
            if (inNutritionSection && !line.trim().isEmpty()) {
                if (line.contains("贴士") || line.contains("小贴士")) {
                    break;
                }
                nutrition.append(line.trim()).append(" ");
            }
        }
        
        return nutrition.toString().trim();
    }
    
    /**
     * 从AI响应中提取烹饪贴士
     */
    private String extractTips(String[] lines) {
        StringBuilder tips = new StringBuilder();
        boolean inTipsSection = false;
        
        for (String line : lines) {
            if (line.contains("贴士") || line.contains("小贴士")) {
                inTipsSection = true;
                continue;
            }
            
            if (inTipsSection && !line.trim().isEmpty()) {
                tips.append(line.trim()).append(" ");
            }
        }
        
        return tips.toString().trim();
    }
    
    /**
     * 估算烹饪时间（分钟）
     */
    private int estimateCookingTime(List<String> steps) {
        // 简单估算：每个步骤约5-10分钟
        return steps.size() * 7;
    }
    
    /**
     * 估算难度级别
     */
    private String estimateDifficulty(List<String> steps) {
        if (steps.size() <= 3) {
            return "简单";
        } else if (steps.size() <= 6) {
            return "中等";
        } else {
            return "困难";
        }
    }
    
    /**
     * 获取当前登录用户的ID
     */
    private UUID getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() != null) {
            // 这里需要根据实际的认证实现来获取用户ID
            // 假设principal是一个包含用户ID的对象
            return UUID.fromString(authentication.getName());
        }
        throw new IllegalStateException("未找到当前登录用户");
    }
}