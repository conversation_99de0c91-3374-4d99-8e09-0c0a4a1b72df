package com.foodmagic.recipe.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 用户偏好覆盖DTO
 * 允许在单次请求中临时覆盖用户的默认偏好
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserPreferencesOverrideDto {
    private List<String> allergies;
    private Boolean isVegetarian;
    private String diet;
    private List<String> preferredCuisines;
}