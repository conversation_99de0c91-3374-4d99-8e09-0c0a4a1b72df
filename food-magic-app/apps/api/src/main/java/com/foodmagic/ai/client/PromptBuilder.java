package com.foodmagic.ai.client;

import com.foodmagic.auth.dto.UserPreferencesDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * AI提示词构建工具类
 * 支持根据用户偏好生成增强的提示词
 */
public class PromptBuilder {
    
    private static final Logger logger = LoggerFactory.getLogger(PromptBuilder.class);
    
    private static final String RECIPE_PROMPT_TEMPLATE = 
        "你是一个专业的美食顾问。请根据以下食材提供美食建议：\n" +
        "食材：%s\n" +
        "请提供：\n" +
        "1. 可以制作的菜品名称\n" +
        "2. 简单的制作步骤\n" +
        "3. 营养价值说明\n" +
        "4. 烹饪小贴士";
    
    private static final String SIMPLE_PROMPT_TEMPLATE = 
        "食材：%s\n请提供相关的美食建议。";
    
    private static final int MAX_PROMPT_LENGTH = 2000; // 最大提示词长度
    
    /**
     * 构建食谱建议提示词
     * @param ingredient 食材
     * @return 格式化的提示词
     */
    public static String buildRecipePrompt(String ingredient) {
        if (ingredient == null || ingredient.trim().isEmpty()) {
            throw new IllegalArgumentException("食材不能为空");
        }
        return String.format(RECIPE_PROMPT_TEMPLATE, ingredient.trim());
    }
    
    /**
     * 构建包含用户偏好的增强提示词
     * @param ingredients 食材列表
     * @param preferences 用户偏好
     * @return 增强的提示词
     */
    public static String buildEnhancedRecipePrompt(List<String> ingredients, UserPreferencesDto preferences) {
        if (ingredients == null || ingredients.isEmpty()) {
            throw new IllegalArgumentException("食材列表不能为空");
        }
        
        StringBuilder promptBuilder = new StringBuilder();
        promptBuilder.append("你是一个专业的美食顾问。");
        
        // 添加硬约束（过敏原）
        if (preferences != null && preferences.getAllergies() != null && !preferences.getAllergies().isEmpty()) {
            promptBuilder.append("\n\n【必须严格遵守】");
            promptBuilder.append("\n绝对不能包含以下过敏原：");
            promptBuilder.append(String.join("、", preferences.getAllergies()));
            promptBuilder.append("。这是最高优先级的要求。");
        }
        
        // 添加饮食目标
        if (preferences != null && preferences.getDiet() != null) {
            promptBuilder.append("\n\n【饮食目标】");
            promptBuilder.append("\n用户正在进行");
            promptBuilder.append(getDietDescription(preferences.getDiet()));
            promptBuilder.append("饮食，请确保食谱符合该饮食方式的要求。");
        }
        
        // 添加素食偏好
        if (preferences != null && preferences.getIsVegetarian() != null && preferences.getIsVegetarian()) {
            promptBuilder.append("\n\n【素食要求】");
            promptBuilder.append("\n用户是素食者，不要包含任何肉类、鱼类或海鲜。");
        }
        
        // 添加菜系偏好（软偏好）
        if (preferences != null && preferences.getPreferredCuisines() != null && !preferences.getPreferredCuisines().isEmpty()) {
            promptBuilder.append("\n\n【风味偏好】");
            promptBuilder.append("\n如果可能，请尝试融合以下菜系风味：");
            promptBuilder.append(preferences.getPreferredCuisines().stream()
                .map(PromptBuilder::getCuisineDescription)
                .collect(Collectors.joining("、")));
            promptBuilder.append("。");
        }
        
        // 添加冲突处理规则
        promptBuilder.append("\n\n【优先级说明】");
        promptBuilder.append("\n如果存在冲突，请按以下优先级处理：");
        promptBuilder.append("\n1. 过敏原限制（最高优先级，必须遵守）");
        promptBuilder.append("\n2. 饮食目标要求（尽量满足）");
        promptBuilder.append("\n3. 菜系风味偏好（在满足前两项的基础上考虑）");
        
        // 添加食材信息
        promptBuilder.append("\n\n【可用食材】");
        promptBuilder.append("\n").append(String.join("、", ingredients));
        
        // 添加输出要求
        promptBuilder.append("\n\n【请提供】");
        promptBuilder.append("\n1. 推荐的菜品名称（考虑用户偏好）");
        promptBuilder.append("\n2. 详细的制作步骤");
        promptBuilder.append("\n3. 营养价值说明（特别关注是否符合饮食目标）");
        promptBuilder.append("\n4. 烹饪小贴士");
        
        String prompt = promptBuilder.toString();
        
        // 检查并优化提示词长度
        if (prompt.length() > MAX_PROMPT_LENGTH) {
            logger.warn("Prompt length ({}) exceeds maximum ({}), optimizing...", prompt.length(), MAX_PROMPT_LENGTH);
            prompt = optimizePromptLength(prompt, ingredients, preferences);
        }
        
        logger.debug("Generated enhanced prompt: {}", prompt);
        return prompt;
    }
    
    /**
     * 获取饮食类型的中文描述
     */
    private static String getDietDescription(String diet) {
        switch (diet) {
            case "LOW_CARB":
                return "低碳水化合物";
            case "HIGH_PROTEIN":
                return "高蛋白";
            case "KETO":
                return "生酮";
            case "PALEO":
                return "原始饮食";
            case "MEDITERRANEAN":
                return "地中海";
            case "VEGAN":
                return "纯素食";
            case "NONE":
            default:
                return "普通";
        }
    }
    
    /**
     * 获取菜系的中文描述
     */
    private static String getCuisineDescription(String cuisine) {
        switch (cuisine) {
            case "CHINESE":
                return "中式";
            case "ITALIAN":
                return "意式";
            case "JAPANESE":
                return "日式";
            case "MEXICAN":
                return "墨西哥";
            case "INDIAN":
                return "印度";
            case "FRENCH":
                return "法式";
            case "THAI":
                return "泰式";
            case "KOREAN":
                return "韩式";
            case "SPANISH":
                return "西班牙";
            case "GREEK":
                return "希腊";
            case "VIETNAMESE":
                return "越南";
            case "MIDDLE_EASTERN":
                return "中东";
            case "AMERICAN":
                return "美式";
            case "BRAZILIAN":
                return "巴西";
            case "AFRICAN":
                return "非洲";
            default:
                return cuisine;
        }
    }
    
    /**
     * 优化提示词长度
     * 当提示词过长时，简化某些部分
     */
    private static String optimizePromptLength(String originalPrompt, List<String> ingredients, UserPreferencesDto preferences) {
        StringBuilder optimizedPrompt = new StringBuilder();
        optimizedPrompt.append("你是专业美食顾问。");
        
        // 保留最重要的硬约束
        if (preferences != null && preferences.getAllergies() != null && !preferences.getAllergies().isEmpty()) {
            optimizedPrompt.append("\n严禁：").append(String.join("、", preferences.getAllergies()));
        }
        
        // 简化饮食目标
        if (preferences != null && preferences.getDiet() != null) {
            optimizedPrompt.append("\n饮食：").append(preferences.getDiet());
        }
        
        // 简化菜系偏好
        if (preferences != null && preferences.getPreferredCuisines() != null && !preferences.getPreferredCuisines().isEmpty()) {
            // 只保留前3个菜系
            List<String> topCuisines = preferences.getPreferredCuisines().stream()
                .limit(3)
                .collect(Collectors.toList());
            optimizedPrompt.append("\n偏好：").append(String.join("、", topCuisines));
        }
        
        // 添加食材
        optimizedPrompt.append("\n食材：").append(String.join("、", ingredients));
        optimizedPrompt.append("\n请提供食谱建议。");
        
        return optimizedPrompt.toString();
    }
    
    /**
     * 构建简单提示词
     * @param ingredient 食材
     * @return 简单格式的提示词
     */
    public static String buildSimplePrompt(String ingredient) {
        if (ingredient == null || ingredient.trim().isEmpty()) {
            throw new IllegalArgumentException("食材不能为空");
        }
        return String.format(SIMPLE_PROMPT_TEMPLATE, ingredient.trim());
    }
    
    /**
     * 构建自定义提示词
     * @param template 提示词模板
     * @param args 参数
     * @return 格式化的提示词
     */
    public static String buildCustomPrompt(String template, Object... args) {
        if (template == null || template.isEmpty()) {
            throw new IllegalArgumentException("提示词模板不能为空");
        }
        return String.format(template, args);
    }
}