package com.foodmagic.user.service;

import com.foodmagic.auth.dto.UserPreferencesDto;
import com.foodmagic.auth.entity.UserPreferencesEntity;
import com.foodmagic.auth.repository.UserPreferencesRepository;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 用户偏好服务
 * 处理用户偏好数据的读取、缓存和更新
 */
@Service
@Transactional(readOnly = true)
public class PreferenceService {
    
    private static final Logger logger = LoggerFactory.getLogger(PreferenceService.class);
    private static final String PREFERENCES_CACHE_KEY = "user_preferences";
    
    @Autowired
    private UserPreferencesRepository userPreferencesRepository;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * 获取用户偏好设置
     * 使用缓存优化性能
     * 
     * @param userId 用户ID
     * @return 用户偏好DTO
     */
    @Cacheable(value = PREFERENCES_CACHE_KEY, key = "#userId")
    public UserPreferencesDto getUserPreferences(UUID userId) {
        logger.debug("Fetching preferences for user: {}", userId);
        
        return userPreferencesRepository.findById(userId)
            .map(this::convertEntityToDto)
            .orElseGet(() -> {
                logger.debug("No preferences found for user: {}, returning defaults", userId);
                return createDefaultPreferences();
            });
    }
    
    /**
     * 更新用户偏好设置
     * 同时清除缓存
     * 
     * @param userId 用户ID
     * @param preferences 新的偏好设置
     * @return 更新后的偏好设置
     */
    @Transactional
    @CacheEvict(value = PREFERENCES_CACHE_KEY, key = "#userId")
    public UserPreferencesDto updateUserPreferences(UUID userId, UserPreferencesDto preferences) {
        logger.info("Updating preferences for user: {}", userId);
        
        UserPreferencesEntity entity = userPreferencesRepository.findById(userId)
            .orElseThrow(() -> new IllegalArgumentException("User preferences not found for userId: " + userId));
        
        // 更新preferences_json字段
        JsonNode updatedJson = convertDtoToJsonNode(preferences);
        entity.setPreferencesJson(updatedJson);
        
        UserPreferencesEntity savedEntity = userPreferencesRepository.save(entity);
        
        // 清除相关的食谱缓存
        evictRecipeCacheForUser(userId);
        
        return convertEntityToDto(savedEntity);
    }
    
    /**
     * 清除用户的食谱缓存
     * 当用户偏好更新时调用
     * 
     * @param userId 用户ID
     */
    @CacheEvict(value = "recipes", allEntries = true)
    public void evictRecipeCacheForUser(UUID userId) {
        logger.debug("Evicting recipe cache for user: {}", userId);
        // 这里简单地清除所有食谱缓存
        // 在生产环境中，可以实现更精细的缓存清除策略
    }
    
    /**
     * 将Entity转换为DTO
     * 
     * @param entity 用户偏好实体
     * @return 用户偏好DTO
     */
    private UserPreferencesDto convertEntityToDto(UserPreferencesEntity entity) {
        JsonNode json = entity.getPreferencesJson();
        UserPreferencesDto dto = new UserPreferencesDto();
        
        // 解析allergies
        if (json.has("allergies") && json.get("allergies").isArray()) {
            List<String> allergies = new ArrayList<>();
            json.get("allergies").forEach(node -> allergies.add(node.asText()));
            dto.setAllergies(allergies);
        }
        
        // 解析isVegetarian
        if (json.has("isVegetarian")) {
            dto.setIsVegetarian(json.get("isVegetarian").asBoolean());
        }
        
        // 解析diet
        if (json.has("diet") && !json.get("diet").isNull()) {
            dto.setDiet(json.get("diet").asText());
        }
        
        // 解析preferredCuisines
        if (json.has("preferredCuisines") && json.get("preferredCuisines").isArray()) {
            List<String> cuisines = new ArrayList<>();
            json.get("preferredCuisines").forEach(node -> cuisines.add(node.asText()));
            dto.setPreferredCuisines(cuisines);
        }
        
        // 解析unitSystem
        if (json.has("unitSystem")) {
            dto.setUnitSystem(json.get("unitSystem").asText());
        }
        
        // 解析locale
        if (json.has("locale")) {
            dto.setLocale(json.get("locale").asText());
        }
        
        return dto;
    }
    
    /**
     * 将DTO转换为JsonNode
     * 
     * @param dto 用户偏好DTO
     * @return JsonNode
     */
    private JsonNode convertDtoToJsonNode(UserPreferencesDto dto) {
        return objectMapper.valueToTree(dto);
    }
    
    /**
     * 创建默认的用户偏好设置
     * 
     * @return 默认的用户偏好DTO
     */
    private UserPreferencesDto createDefaultPreferences() {
        UserPreferencesDto dto = new UserPreferencesDto();
        dto.setAllergies(new ArrayList<>());
        dto.setIsVegetarian(false);
        dto.setDiet(null);
        dto.setPreferredCuisines(new ArrayList<>());
        dto.setUnitSystem("METRIC");
        dto.setLocale("zh-CN");
        return dto;
    }
    
    /**
     * 生成包含用户偏好的缓存键
     * 用于食谱缓存
     * 
     * @param userId 用户ID
     * @param ingredients 食材列表
     * @return 缓存键
     */
    public String generateRecipeCacheKey(UUID userId, List<String> ingredients) {
        UserPreferencesDto preferences = getUserPreferences(userId);
        
        StringBuilder keyBuilder = new StringBuilder("recipe:");
        keyBuilder.append(userId).append(":");
        
        // 添加食材
        keyBuilder.append(String.join(",", ingredients)).append(":");
        
        // 添加偏好信息
        if (preferences.getDiet() != null) {
            keyBuilder.append("diet:").append(preferences.getDiet()).append(":");
        }
        
        if (preferences.getPreferredCuisines() != null && !preferences.getPreferredCuisines().isEmpty()) {
            keyBuilder.append("cuisines:").append(String.join(",", preferences.getPreferredCuisines())).append(":");
        }
        
        if (preferences.getAllergies() != null && !preferences.getAllergies().isEmpty()) {
            keyBuilder.append("allergies:").append(String.join(",", preferences.getAllergies())).append(":");
        }
        
        if (preferences.getIsVegetarian() != null && preferences.getIsVegetarian()) {
            keyBuilder.append("vegetarian:true:");
        }
        
        keyBuilder.append("locale:").append(preferences.getLocale() != null ? preferences.getLocale() : "zh-CN");
        keyBuilder.append(":unit:").append(preferences.getUnitSystem() != null ? preferences.getUnitSystem() : "METRIC");
        
        String cacheKey = keyBuilder.toString();
        logger.debug("Generated recipe cache key: {}", cacheKey);
        
        return cacheKey;
    }
}