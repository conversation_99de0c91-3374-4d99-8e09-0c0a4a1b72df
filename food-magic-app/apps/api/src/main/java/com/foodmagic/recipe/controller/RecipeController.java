package com.foodmagic.recipe.controller;

import com.foodmagic.recipe.dto.GenerateRecipeRequestDto;
import com.foodmagic.recipe.dto.RecipeDto;
import com.foodmagic.recipe.service.RecipeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

/**
 * 食谱控制器
 * 处理食谱生成相关的API请求
 */
@RestController
@RequestMapping("/api/v1/recipes")
@Tag(name = "Recipe", description = "食谱生成API - 基于用户偏好生成个性化食谱")
@Validated
@SecurityRequirement(name = "bearerAuth")
public class RecipeController {
    
    private static final Logger logger = LoggerFactory.getLogger(RecipeController.class);
    
    @Autowired
    private RecipeService recipeService;
    
    @PostMapping("/generate")
    @Operation(
        summary = "生成个性化食谱",
        description = "根据提供的食材和用户偏好设置生成个性化食谱"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "成功生成食谱",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = RecipeDto.class)
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "请求参数错误",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "401",
            description = "未授权访问",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "服务器内部错误",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "503",
            description = "AI服务不可用",
            content = @Content(mediaType = "application/json")
        )
    })
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<RecipeDto> generateRecipe(
            @Valid @RequestBody GenerateRecipeRequestDto request) {
        
        logger.info("Received recipe generation request with {} ingredients", 
            request.getIngredients().size());
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 生成食谱，集成用户偏好
            RecipeDto recipe = recipeService.generateRecipeForCurrentUser(request);
            
            long processingTime = System.currentTimeMillis() - startTime;
            logger.info("Successfully generated recipe '{}' in {}ms", 
                recipe.getName(), processingTime);
            
            return ResponseEntity.ok(recipe);
            
        } catch (IllegalStateException e) {
            // 用户未登录或无法获取用户信息
            logger.error("Authentication error: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
            
        } catch (RuntimeException e) {
            // AI服务调用失败或其他运行时错误
            logger.error("Failed to generate recipe: {}", e.getMessage(), e);
            
            if (e.getMessage().contains("AI服务")) {
                return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).build();
            }
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
            
        } catch (Exception e) {
            // 其他未预期错误
            logger.error("Unexpected error generating recipe: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @PostMapping("/generate/{userId}")
    @Operation(
        summary = "为指定用户生成食谱（管理员）",
        description = "管理员可以为指定用户生成食谱，用于测试或支持"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "成功生成食谱",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = RecipeDto.class)
            )
        ),
        @ApiResponse(
            responseCode = "403",
            description = "权限不足",
            content = @Content(mediaType = "application/json")
        )
    })
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<RecipeDto> generateRecipeForUser(
            @PathVariable UUID userId,
            @Valid @RequestBody GenerateRecipeRequestDto request) {
        
        logger.info("Admin generating recipe for user: {}", userId);
        
        try {
            RecipeDto recipe = recipeService.generateRecipe(request, userId);
            return ResponseEntity.ok(recipe);
            
        } catch (Exception e) {
            logger.error("Failed to generate recipe for user {}: {}", userId, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @GetMapping("/health")
    @Operation(
        summary = "健康检查",
        description = "检查食谱服务是否正常工作"
    )
    @ApiResponse(
        responseCode = "200",
        description = "服务正常",
        content = @Content(mediaType = "text/plain")
    )
    public ResponseEntity<String> healthCheck() {
        return ResponseEntity.ok("Recipe service is healthy");
    }
}