spring:
  application:
    name: food-magic-api
  
  # 数据源配置
  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:foodmagic}
    username: ${DB_USERNAME:foodmagic}
    password: ${DB_PASSWORD:foodmagic123}
    driver-class-name: org.postgresql.Driver
    hikari:
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      maximum-pool-size: 10
      minimum-idle: 5
      pool-name: FoodMagicHikariPool
  
  # JPA 配置
  jpa:
    database: POSTGRESQL
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: validate
    properties:
      hibernate:
        format_sql: true
        jdbc:
          lob:
            non_contextual_creation: true
        temp:
          use_jdbc_metadata_defaults: false
    show-sql: false
    open-in-view: false
  
  # Flyway 数据库迁移配置
  flyway:
    enabled: true
    baseline-on-migrate: true
    locations: classpath:db/migration
    validate-on-migrate: true
  
  # Jackson 配置
  jackson:
    serialization:
      write-dates-as-timestamps: false
    time-zone: UTC
    date-format: yyyy-MM-dd'T'HH:mm:ss.SSS'Z'
  
  # Spring Security 基础配置
  security:
    user:
      name: ${ADMIN_USERNAME:admin}
      password: ${ADMIN_PASSWORD:admin}

# JWT 配置
jwt:
  secret: ${JWT_SECRET:ThisIsAVerySecretKeyForDevelopmentOnly}
  expiration: ${JWT_EXPIRATION:86400000}  # 24 hours in milliseconds
  refresh-expiration: ${JWT_REFRESH_EXPIRATION:604800000}  # 7 days in milliseconds

# 服务器配置
server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: /api/v1
  error:
    include-message: always
    include-binding-errors: always

# 日志配置
logging:
  level:
    com.foodmagic: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# OpenAPI 配置
springdoc:
  api-docs:
    path: /api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
    operations-sorter: method
    tags-sorter: alpha
  packages-to-scan: com.foodmagic
  paths-to-match: /**

# CORS 配置
cors:
  allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:8081}
  allowed-methods: GET,POST,PUT,DELETE,OPTIONS
  allowed-headers: "*"
  allow-credentials: true
  max-age: 3600

# AI 服务配置
ai:
  service:
    provider: ${AI_PROVIDER:zhipu}  # 支持 zhipu (智谱AI) 或 qwen (阿里通义千问)
    api-key: ${AI_API_KEY:}  # 从环境变量加载，必须配置
    api-url: ${AI_API_URL:}  # 从环境变量加载，必须配置
    model: ${AI_MODEL:glm-4}  # 默认使用 glm-4
    max-tokens: ${AI_MAX_TOKENS:2000}
    temperature: ${AI_TEMPERATURE:0.7}
    timeout: ${AI_TIMEOUT:30000}  # 30秒超时
    max-retries: ${AI_MAX_RETRIES:3}
    retry-delay: ${AI_RETRY_DELAY:1000}  # 重试延迟（毫秒）

# Resilience4j 配置
resilience4j:
  circuitbreaker:
    instances:
      aiService:
        register-health-indicator: true
        failure-rate-threshold: 50  # 失败率阈值
        slow-call-rate-threshold: 100  # 慢调用率阈值
        slow-call-duration-threshold: 10s  # 慢调用时间阈值
        sliding-window-size: 10  # 滑动窗口大小
        minimum-number-of-calls: 5  # 最小调用次数
        permitted-number-of-calls-in-half-open-state: 3  # 半开状态允许的调用次数
        automatic-transition-from-open-to-half-open-enabled: true
        wait-duration-in-open-state: 30s  # 开路状态等待时间
        record-exceptions:
          - java.io.IOException
          - java.util.concurrent.TimeoutException
          - java.lang.RuntimeException
  retry:
    instances:
      aiService:
        max-attempts: 3
        wait-duration: 1s
        retry-exceptions:
          - java.io.IOException
          - java.util.concurrent.TimeoutException

# 应用配置
app:
  version: 1.0.0
  environment: ${APP_ENV:development}