package com.foodmagic.user.service;

import com.foodmagic.auth.dto.UserPreferencesDto;
import com.foodmagic.auth.entity.UserPreferencesEntity;
import com.foodmagic.auth.repository.UserPreferencesRepository;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * PreferenceService单元测试
 * 验证偏好数据的读取、缓存和更新逻辑
 */
@ExtendWith(MockitoExtension.class)
class PreferenceServiceTest {
    
    @Mock
    private UserPreferencesRepository userPreferencesRepository;
    
    @InjectMocks
    private PreferenceService preferenceService;
    
    private ObjectMapper objectMapper;
    private UUID testUserId;
    
    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        testUserId = UUID.randomUUID();
        ReflectionTestUtils.setField(preferenceService, "objectMapper", objectMapper);
    }
    
    @Test
    @DisplayName("测试获取用户偏好 - 存在偏好数据")
    void testGetUserPreferencesWithExistingData() {
        // 准备测试数据
        UserPreferencesEntity entity = createTestEntity();
        when(userPreferencesRepository.findById(testUserId)).thenReturn(Optional.of(entity));
        
        // 执行测试
        UserPreferencesDto result = preferenceService.getUserPreferences(testUserId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getAllergies().size());
        assertTrue(result.getAllergies().contains("花生"));
        assertTrue(result.getAllergies().contains("海鲜"));
        assertEquals("KETO", result.getDiet());
        assertEquals(2, result.getPreferredCuisines().size());
        assertTrue(result.getIsVegetarian());
        assertEquals("METRIC", result.getUnitSystem());
        assertEquals("zh-CN", result.getLocale());
        
        verify(userPreferencesRepository, times(1)).findById(testUserId);
    }
    
    @Test
    @DisplayName("测试获取用户偏好 - 不存在偏好数据返回默认值")
    void testGetUserPreferencesWithNoData() {
        // 模拟没有找到数据
        when(userPreferencesRepository.findById(testUserId)).thenReturn(Optional.empty());
        
        // 执行测试
        UserPreferencesDto result = preferenceService.getUserPreferences(testUserId);
        
        // 验证返回默认值
        assertNotNull(result);
        assertNotNull(result.getAllergies());
        assertTrue(result.getAllergies().isEmpty());
        assertFalse(result.getIsVegetarian());
        assertNull(result.getDiet());
        assertTrue(result.getPreferredCuisines().isEmpty());
        assertEquals("METRIC", result.getUnitSystem());
        assertEquals("zh-CN", result.getLocale());
        
        verify(userPreferencesRepository, times(1)).findById(testUserId);
    }
    
    @Test
    @DisplayName("测试更新用户偏好")
    void testUpdateUserPreferences() {
        // 准备测试数据
        UserPreferencesEntity existingEntity = createTestEntity();
        when(userPreferencesRepository.findById(testUserId)).thenReturn(Optional.of(existingEntity));
        when(userPreferencesRepository.save(any(UserPreferencesEntity.class))).thenReturn(existingEntity);
        
        UserPreferencesDto newPreferences = new UserPreferencesDto();
        newPreferences.setAllergies(Arrays.asList("牛奶", "鸡蛋"));
        newPreferences.setDiet("HIGH_PROTEIN");
        newPreferences.setPreferredCuisines(Arrays.asList("ITALIAN", "FRENCH"));
        newPreferences.setIsVegetarian(false);
        newPreferences.setUnitSystem("IMPERIAL");
        newPreferences.setLocale("en-US");
        
        // 执行测试
        UserPreferencesDto result = preferenceService.updateUserPreferences(testUserId, newPreferences);
        
        // 验证结果
        assertNotNull(result);
        verify(userPreferencesRepository, times(1)).findById(testUserId);
        verify(userPreferencesRepository, times(1)).save(any(UserPreferencesEntity.class));
    }
    
    @Test
    @DisplayName("测试更新不存在的用户偏好抛出异常")
    void testUpdateNonExistentUserPreferences() {
        // 模拟用户不存在
        when(userPreferencesRepository.findById(testUserId)).thenReturn(Optional.empty());
        
        UserPreferencesDto newPreferences = new UserPreferencesDto();
        
        // 验证抛出异常
        assertThrows(IllegalArgumentException.class, () -> {
            preferenceService.updateUserPreferences(testUserId, newPreferences);
        });
        
        verify(userPreferencesRepository, times(1)).findById(testUserId);
        verify(userPreferencesRepository, never()).save(any());
    }
    
    @Test
    @DisplayName("测试生成包含偏好的缓存键")
    void testGenerateRecipeCacheKey() {
        // 准备测试数据
        UserPreferencesEntity entity = createTestEntity();
        when(userPreferencesRepository.findById(testUserId)).thenReturn(Optional.of(entity));
        
        // 执行测试
        String cacheKey = preferenceService.generateRecipeCacheKey(
            testUserId, 
            Arrays.asList("鸡肉", "青椒")
        );
        
        // 验证缓存键包含必要信息
        assertNotNull(cacheKey);
        assertTrue(cacheKey.startsWith("recipe:"));
        assertTrue(cacheKey.contains(testUserId.toString()));
        assertTrue(cacheKey.contains("鸡肉"));
        assertTrue(cacheKey.contains("青椒"));
        assertTrue(cacheKey.contains("diet:KETO"));
        assertTrue(cacheKey.contains("cuisines:CHINESE,JAPANESE"));
        assertTrue(cacheKey.contains("allergies:花生,海鲜"));
        assertTrue(cacheKey.contains("vegetarian:true"));
        assertTrue(cacheKey.contains("locale:zh-CN"));
        assertTrue(cacheKey.contains("unit:METRIC"));
    }
    
    @Test
    @DisplayName("测试生成缓存键 - 空偏好")
    void testGenerateRecipeCacheKeyWithEmptyPreferences() {
        // 准备空偏好数据
        UserPreferencesEntity entity = new UserPreferencesEntity();
        entity.setUserId(testUserId);
        entity.setPreferencesJson(createEmptyPreferencesJson());
        when(userPreferencesRepository.findById(testUserId)).thenReturn(Optional.of(entity));
        
        // 执行测试
        String cacheKey = preferenceService.generateRecipeCacheKey(
            testUserId, 
            Arrays.asList("土豆")
        );
        
        // 验证基础缓存键
        assertNotNull(cacheKey);
        assertTrue(cacheKey.startsWith("recipe:"));
        assertTrue(cacheKey.contains(testUserId.toString()));
        assertTrue(cacheKey.contains("土豆"));
        assertFalse(cacheKey.contains("diet:"));
        assertFalse(cacheKey.contains("cuisines:"));
        assertFalse(cacheKey.contains("allergies:"));
        assertFalse(cacheKey.contains("vegetarian:true"));
    }
    
    @Test
    @DisplayName("测试缓存键对不同偏好组合的区分")
    void testCacheKeyDistinctionForDifferentPreferences() {
        // 创建两个不同的偏好
        UserPreferencesEntity entity1 = createTestEntity();
        UserPreferencesEntity entity2 = createTestEntity();
        
        // 修改第二个实体的偏好
        ObjectNode json2 = (ObjectNode) entity2.getPreferencesJson();
        json2.put("diet", "LOW_CARB");
        
        // 第一次调用
        when(userPreferencesRepository.findById(testUserId)).thenReturn(Optional.of(entity1));
        String cacheKey1 = preferenceService.generateRecipeCacheKey(
            testUserId, 
            Arrays.asList("鸡肉")
        );
        
        // 第二次调用（不同偏好）
        when(userPreferencesRepository.findById(testUserId)).thenReturn(Optional.of(entity2));
        String cacheKey2 = preferenceService.generateRecipeCacheKey(
            testUserId, 
            Arrays.asList("鸡肉")
        );
        
        // 验证两个缓存键不同
        assertNotEquals(cacheKey1, cacheKey2);
        assertTrue(cacheKey1.contains("diet:KETO"));
        assertTrue(cacheKey2.contains("diet:LOW_CARB"));
    }
    
    /**
     * 创建测试用的UserPreferencesEntity
     */
    private UserPreferencesEntity createTestEntity() {
        UserPreferencesEntity entity = new UserPreferencesEntity();
        entity.setUserId(testUserId);
        
        ObjectNode preferencesJson = objectMapper.createObjectNode();
        
        // 添加过敏原
        ArrayNode allergies = preferencesJson.putArray("allergies");
        allergies.add("花生");
        allergies.add("海鲜");
        
        // 添加其他偏好
        preferencesJson.put("isVegetarian", true);
        preferencesJson.put("diet", "KETO");
        
        // 添加菜系偏好
        ArrayNode cuisines = preferencesJson.putArray("preferredCuisines");
        cuisines.add("CHINESE");
        cuisines.add("JAPANESE");
        
        preferencesJson.put("unitSystem", "METRIC");
        preferencesJson.put("locale", "zh-CN");
        
        entity.setPreferencesJson(preferencesJson);
        
        return entity;
    }
    
    /**
     * 创建空的偏好JSON
     */
    private JsonNode createEmptyPreferencesJson() {
        ObjectNode json = objectMapper.createObjectNode();
        json.putArray("allergies");
        json.put("isVegetarian", false);
        json.putNull("diet");
        json.putArray("preferredCuisines");
        json.put("unitSystem", "METRIC");
        json.put("locale", "zh-CN");
        return json;
    }
}