package com.foodmagic.recipe.integration;

import com.foodmagic.ai.service.AiServiceClient;
import com.foodmagic.auth.dto.UserPreferencesDto;
import com.foodmagic.recipe.dto.GenerateRecipeRequestDto;
import com.foodmagic.recipe.dto.RecipeDto;
import com.foodmagic.recipe.service.RecipeService;
import com.foodmagic.user.service.PreferenceService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 食谱生成集成测试
 * 验证完整的食谱生成流程包含用户偏好集成
 */
@SpringBootTest
@ActiveProfiles("test")
class RecipeGenerationIntegrationTest {
    
    @Autowired
    private RecipeService recipeService;
    
    @MockBean
    private AiServiceClient aiServiceClient;
    
    @MockBean
    private PreferenceService preferenceService;
    
    private UUID testUserId;
    private UserPreferencesDto testPreferences;
    
    @BeforeEach
    void setUp() {
        testUserId = UUID.randomUUID();
        
        // 设置测试偏好
        testPreferences = new UserPreferencesDto();
        testPreferences.setAllergies(Arrays.asList("花生", "虾"));
        testPreferences.setDiet("KETO");
        testPreferences.setPreferredCuisines(Arrays.asList("CHINESE", "ITALIAN"));
        testPreferences.setIsVegetarian(false);
        testPreferences.setUnitSystem("METRIC");
        testPreferences.setLocale("zh-CN");
    }
    
    @Test
    @DisplayName("集成测试 - 生成包含用户偏好的食谱")
    void testGenerateRecipeWithUserPreferences() {
        // 准备测试数据
        GenerateRecipeRequestDto request = new GenerateRecipeRequestDto();
        request.setIngredients(Arrays.asList("鸡肉", "西兰花", "橄榄油"));
        
        // 模拟偏好服务返回
        when(preferenceService.getUserPreferences(testUserId)).thenReturn(testPreferences);
        when(preferenceService.generateRecipeCacheKey(eq(testUserId), anyList()))
            .thenReturn("recipe:test:key");
        
        // 模拟AI服务返回
        String aiResponse = createMockAiResponse();
        when(aiServiceClient.generateResponse(anyString())).thenReturn(aiResponse);
        
        // 执行测试
        RecipeDto result = recipeService.generateRecipe(request, testUserId);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getName());
        assertNotNull(result.getDescription());
        assertEquals(3, result.getIngredients().size());
        assertNotNull(result.getSteps());
        assertFalse(result.getSteps().isEmpty());
        assertEquals("KETO", result.getDietType());
        assertEquals(Arrays.asList("CHINESE", "ITALIAN"), result.getCuisineTypes());
        assertFalse(result.getIsVegetarian());
        
        // 验证方法调用
        verify(preferenceService, times(1)).getUserPreferences(testUserId);
        verify(aiServiceClient, times(1)).generateResponse(argThat(prompt -> {
            // 验证prompt包含偏好信息
            return prompt.contains("花生") && 
                   prompt.contains("虾") &&
                   prompt.contains("生酮") &&
                   prompt.contains("中式") &&
                   prompt.contains("意式");
        }));
    }
    
    @Test
    @DisplayName("集成测试 - 验证生酮饮食偏好影响食谱内容")
    void testKetoDietPreferenceAffectsRecipeContent() {
        // 设置生酮饮食偏好
        testPreferences.setDiet("KETO");
        when(preferenceService.getUserPreferences(testUserId)).thenReturn(testPreferences);
        when(preferenceService.generateRecipeCacheKey(eq(testUserId), anyList()))
            .thenReturn("recipe:keto:key");
        
        // 模拟AI返回符合生酮饮食的食谱
        String ketoResponse = createKetoRecipeResponse();
        when(aiServiceClient.generateResponse(anyString())).thenReturn(ketoResponse);
        
        GenerateRecipeRequestDto request = new GenerateRecipeRequestDto();
        request.setIngredients(Arrays.asList("牛肉", "黄油", "菠菜"));
        
        // 执行测试
        RecipeDto result = recipeService.generateRecipe(request, testUserId);
        
        // 验证结果符合生酮饮食特征
        assertNotNull(result);
        assertEquals("KETO", result.getDietType());
        assertTrue(result.getNutrition().contains("低碳水"));
        assertTrue(result.getNutrition().contains("高脂肪"));
        
        // 验证AI调用包含生酮相关提示
        verify(aiServiceClient, times(1)).generateResponse(argThat(prompt -> 
            prompt.contains("生酮") && prompt.contains("饮食目标")
        ));
    }
    
    @Test
    @DisplayName("集成测试 - 验证过敏原硬约束")
    void testAllergyConstraintsAreRespected() {
        // 设置过敏原
        testPreferences.setAllergies(Arrays.asList("坚果", "贝类"));
        when(preferenceService.getUserPreferences(testUserId)).thenReturn(testPreferences);
        when(preferenceService.generateRecipeCacheKey(eq(testUserId), anyList()))
            .thenReturn("recipe:allergy:key");
        
        // 模拟AI返回不含过敏原的食谱
        String safeResponse = createAllergyFreeResponse();
        when(aiServiceClient.generateResponse(anyString())).thenReturn(safeResponse);
        
        GenerateRecipeRequestDto request = new GenerateRecipeRequestDto();
        request.setIngredients(Arrays.asList("鸡肉", "蔬菜"));
        
        // 执行测试
        RecipeDto result = recipeService.generateRecipe(request, testUserId);
        
        // 验证结果
        assertNotNull(result);
        // 验证食谱中不包含过敏原
        assertFalse(result.getDescription().contains("坚果"));
        assertFalse(result.getDescription().contains("贝类"));
        
        // 验证prompt强调了过敏原约束
        verify(aiServiceClient, times(1)).generateResponse(argThat(prompt -> 
            prompt.contains("必须严格遵守") && 
            prompt.contains("坚果") && 
            prompt.contains("贝类")
        ));
    }
    
    @Test
    @DisplayName("集成测试 - 验证素食偏好")
    void testVegetarianPreference() {
        // 设置素食偏好
        testPreferences.setIsVegetarian(true);
        testPreferences.setDiet(null);
        testPreferences.setAllergies(null);
        when(preferenceService.getUserPreferences(testUserId)).thenReturn(testPreferences);
        when(preferenceService.generateRecipeCacheKey(eq(testUserId), anyList()))
            .thenReturn("recipe:vegetarian:key");
        
        // 模拟AI返回素食食谱
        String vegetarianResponse = createVegetarianRecipeResponse();
        when(aiServiceClient.generateResponse(anyString())).thenReturn(vegetarianResponse);
        
        GenerateRecipeRequestDto request = new GenerateRecipeRequestDto();
        request.setIngredients(Arrays.asList("豆腐", "蘑菇", "青菜"));
        
        // 执行测试
        RecipeDto result = recipeService.generateRecipe(request, testUserId);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.getIsVegetarian());
        assertTrue(result.getName().contains("素"));
        
        // 验证prompt包含素食要求
        verify(aiServiceClient, times(1)).generateResponse(argThat(prompt -> 
            prompt.contains("素食") && prompt.contains("不要包含任何肉类")
        ));
    }
    
    @Test
    @DisplayName("集成测试 - 验证缓存键包含偏好信息")
    void testCacheKeyIncludesPreferences() {
        // 设置完整偏好
        when(preferenceService.getUserPreferences(testUserId)).thenReturn(testPreferences);
        
        // 生成缓存键
        String expectedCacheKey = String.format(
            "recipe:%s:鸡肉,土豆:diet:KETO:cuisines:CHINESE,ITALIAN:allergies:花生,虾:locale:zh-CN:unit:METRIC",
            testUserId
        );
        when(preferenceService.generateRecipeCacheKey(eq(testUserId), anyList()))
            .thenReturn(expectedCacheKey);
        
        when(aiServiceClient.generateResponse(anyString())).thenReturn(createMockAiResponse());
        
        GenerateRecipeRequestDto request = new GenerateRecipeRequestDto();
        request.setIngredients(Arrays.asList("鸡肉", "土豆"));
        
        // 执行测试
        recipeService.generateRecipe(request, testUserId);
        
        // 验证缓存键生成被调用
        verify(preferenceService, times(1)).generateRecipeCacheKey(testUserId, request.getIngredients());
    }
    
    /**
     * 创建模拟的AI响应
     */
    private String createMockAiResponse() {
        return "推荐菜品名称：生酮鸡肉西兰花\n" +
               "制作步骤：\n" +
               "1. 将鸡肉切块，用橄榄油腌制\n" +
               "2. 西兰花焯水备用\n" +
               "3. 热锅放橄榄油，爆炒鸡肉\n" +
               "4. 加入西兰花一起炒制\n" +
               "5. 调味装盘\n" +
               "营养价值：富含蛋白质，低碳水化合物，适合生酮饮食\n" +
               "烹饪贴士：注意火候控制，保持食材新鲜口感";
    }
    
    /**
     * 创建生酮食谱响应
     */
    private String createKetoRecipeResponse() {
        return "推荐菜品名称：黄油煎牛排配菠菜\n" +
               "制作步骤：\n" +
               "1. 牛排用黄油煎制\n" +
               "2. 菠菜用黄油炒制\n" +
               "营养价值：低碳水（2g），高脂肪（45g），适量蛋白质（35g），完美符合生酮饮食要求\n" +
               "烹饪贴士：使用草饲黄油效果更佳";
    }
    
    /**
     * 创建无过敏原食谱响应
     */
    private String createAllergyFreeResponse() {
        return "推荐菜品名称：清炒时蔬鸡丁\n" +
               "制作步骤：\n" +
               "1. 鸡肉切丁\n" +
               "2. 蔬菜切块\n" +
               "3. 清炒调味\n" +
               "营养价值：不含坚果和贝类，安全健康\n" +
               "烹饪贴士：使用新鲜食材";
    }
    
    /**
     * 创建素食食谱响应
     */
    private String createVegetarianRecipeResponse() {
        return "推荐菜品名称：香菇烧豆腐素食煲\n" +
               "制作步骤：\n" +
               "1. 豆腐切块\n" +
               "2. 香菇切片\n" +
               "3. 素食烹饪\n" +
               "营养价值：纯素食，富含植物蛋白\n" +
               "烹饪贴士：使用素食调料";
    }
}