package com.foodmagic.ai.client;

import com.foodmagic.auth.dto.UserPreferencesDto;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * PromptBuilder单元测试
 * 验证prompt构造逻辑的正确性
 */
class PromptBuilderTest {
    
    @Test
    @DisplayName("测试基础食谱提示词构建")
    void testBuildRecipePrompt() {
        String ingredient = "土豆";
        String prompt = PromptBuilder.buildRecipePrompt(ingredient);
        
        assertNotNull(prompt);
        assertTrue(prompt.contains("土豆"));
        assertTrue(prompt.contains("美食顾问"));
        assertTrue(prompt.contains("制作步骤"));
    }
    
    @Test
    @DisplayName("测试增强提示词 - 包含过敏原（硬约束）")
    void testBuildEnhancedPromptWithAllergies() {
        List<String> ingredients = Arrays.asList("鸡肉", "青椒", "洋葱");
        UserPreferencesDto preferences = new UserPreferencesDto();
        preferences.setAllergies(Arrays.asList("花生", "海鲜"));
        
        String prompt = PromptBuilder.buildEnhancedRecipePrompt(ingredients, preferences);
        
        assertNotNull(prompt);
        assertTrue(prompt.contains("必须严格遵守"));
        assertTrue(prompt.contains("花生"));
        assertTrue(prompt.contains("海鲜"));
        assertTrue(prompt.contains("鸡肉"));
    }
    
    @Test
    @DisplayName("测试增强提示词 - 包含饮食目标")
    void testBuildEnhancedPromptWithDietType() {
        List<String> ingredients = Arrays.asList("牛肉", "生菜");
        UserPreferencesDto preferences = new UserPreferencesDto();
        preferences.setDiet("KETO");
        
        String prompt = PromptBuilder.buildEnhancedRecipePrompt(ingredients, preferences);
        
        assertNotNull(prompt);
        assertTrue(prompt.contains("饮食目标"));
        assertTrue(prompt.contains("生酮"));
        assertTrue(prompt.contains("饮食方式"));
    }
    
    @Test
    @DisplayName("测试增强提示词 - 素食偏好")
    void testBuildEnhancedPromptWithVegetarian() {
        List<String> ingredients = Arrays.asList("豆腐", "蘑菇");
        UserPreferencesDto preferences = new UserPreferencesDto();
        preferences.setIsVegetarian(true);
        
        String prompt = PromptBuilder.buildEnhancedRecipePrompt(ingredients, preferences);
        
        assertNotNull(prompt);
        assertTrue(prompt.contains("素食要求"));
        assertTrue(prompt.contains("不要包含任何肉类"));
    }
    
    @Test
    @DisplayName("测试增强提示词 - 菜系偏好（软偏好）")
    void testBuildEnhancedPromptWithCuisines() {
        List<String> ingredients = Arrays.asList("虾", "面条");
        UserPreferencesDto preferences = new UserPreferencesDto();
        preferences.setPreferredCuisines(Arrays.asList("ITALIAN", "CHINESE"));
        
        String prompt = PromptBuilder.buildEnhancedRecipePrompt(ingredients, preferences);
        
        assertNotNull(prompt);
        assertTrue(prompt.contains("风味偏好"));
        assertTrue(prompt.contains("意式"));
        assertTrue(prompt.contains("中式"));
    }
    
    @Test
    @DisplayName("测试增强提示词 - 完整偏好组合")
    void testBuildEnhancedPromptWithFullPreferences() {
        List<String> ingredients = Arrays.asList("鸡肉", "西兰花", "糙米");
        UserPreferencesDto preferences = new UserPreferencesDto();
        preferences.setAllergies(Arrays.asList("坚果"));
        preferences.setDiet("HIGH_PROTEIN");
        preferences.setPreferredCuisines(Arrays.asList("JAPANESE", "THAI"));
        preferences.setIsVegetarian(false);
        
        String prompt = PromptBuilder.buildEnhancedRecipePrompt(ingredients, preferences);
        
        assertNotNull(prompt);
        // 验证优先级顺序
        assertTrue(prompt.contains("必须严格遵守")); // 过敏原最高优先级
        assertTrue(prompt.contains("高蛋白")); // 饮食目标
        assertTrue(prompt.contains("日式")); // 菜系偏好
        assertTrue(prompt.contains("泰式"));
        assertTrue(prompt.contains("优先级说明")); // 冲突处理规则
    }
    
    @Test
    @DisplayName("测试提示词长度优化")
    void testPromptLengthOptimization() {
        // 创建超长的偏好列表以触发优化
        List<String> ingredients = Arrays.asList("食材1", "食材2", "食材3", "食材4", "食材5",
            "食材6", "食材7", "食材8", "食材9", "食材10");
        UserPreferencesDto preferences = new UserPreferencesDto();
        preferences.setAllergies(Arrays.asList("过敏原1", "过敏原2", "过敏原3", "过敏原4", "过敏原5"));
        preferences.setPreferredCuisines(Arrays.asList("CHINESE", "ITALIAN", "JAPANESE", "MEXICAN", 
            "INDIAN", "FRENCH", "THAI")); // 超过3个菜系
        
        String prompt = PromptBuilder.buildEnhancedRecipePrompt(ingredients, preferences);
        
        assertNotNull(prompt);
        // 验证prompt没有超过最大长度（2000字符）
        assertTrue(prompt.length() <= 2000);
    }
    
    @Test
    @DisplayName("测试空食材列表异常")
    void testEmptyIngredientsException() {
        UserPreferencesDto preferences = new UserPreferencesDto();
        
        assertThrows(IllegalArgumentException.class, () -> {
            PromptBuilder.buildEnhancedRecipePrompt(null, preferences);
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            PromptBuilder.buildEnhancedRecipePrompt(Arrays.asList(), preferences);
        });
    }
    
    @Test
    @DisplayName("测试null偏好处理")
    void testNullPreferences() {
        List<String> ingredients = Arrays.asList("鸡蛋", "番茄");
        
        // 应该能处理null偏好
        String prompt = PromptBuilder.buildEnhancedRecipePrompt(ingredients, null);
        
        assertNotNull(prompt);
        assertTrue(prompt.contains("鸡蛋"));
        assertTrue(prompt.contains("番茄"));
        // 不应该包含偏好相关内容
        assertFalse(prompt.contains("过敏原"));
        assertFalse(prompt.contains("饮食目标"));
    }
    
    @Test
    @DisplayName("测试饮食类型描述转换")
    void testDietTypeDescriptions() {
        List<String> ingredients = Arrays.asList("食材");
        
        // 测试所有饮食类型
        String[] dietTypes = {"LOW_CARB", "HIGH_PROTEIN", "KETO", "PALEO", "MEDITERRANEAN", "VEGAN", "NONE"};
        String[] expectedDescriptions = {"低碳水化合物", "高蛋白", "生酮", "原始饮食", "地中海", "纯素食", "普通"};
        
        for (int i = 0; i < dietTypes.length; i++) {
            UserPreferencesDto preferences = new UserPreferencesDto();
            preferences.setDiet(dietTypes[i]);
            
            String prompt = PromptBuilder.buildEnhancedRecipePrompt(ingredients, preferences);
            assertTrue(prompt.contains(expectedDescriptions[i]), 
                "Diet type " + dietTypes[i] + " should contain " + expectedDescriptions[i]);
        }
    }
    
    @Test
    @DisplayName("测试菜系类型描述转换")
    void testCuisineTypeDescriptions() {
        List<String> ingredients = Arrays.asList("食材");
        UserPreferencesDto preferences = new UserPreferencesDto();
        preferences.setPreferredCuisines(Arrays.asList("CHINESE", "ITALIAN", "JAPANESE"));
        
        String prompt = PromptBuilder.buildEnhancedRecipePrompt(ingredients, preferences);
        
        assertTrue(prompt.contains("中式"));
        assertTrue(prompt.contains("意式"));
        assertTrue(prompt.contains("日式"));
    }
}