package com.foodmagic.ai.client;

import com.foodmagic.user.dto.UserPreferencesDto;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Builder class for constructing AI prompts with user preferences integration.
 * Converts user dietary preferences into AI-understandable instructions.
 * 
 * <AUTHOR> (Dev Agent)
 * @since 1.0
 */
@Component
public class PromptBuilder {

    private static final String PROMPT_TEMPLATE = """
        You are a professional chef creating a recipe based on the following requirements:
        
        Available Ingredients: %s
        
        %s
        
        Please provide a complete recipe including:
        1. Recipe name
        2. Preparation time and cooking time
        3. Serving size
        4. Complete ingredient list with measurements
        5. Step-by-step cooking instructions
        6. Nutritional information (approximate)
        7. Tips or variations (optional)
        
        Format the response in a clear, structured manner.
        """;

    /**
     * Build a recipe generation prompt with user preferences.
     * 
     * @param ingredients list of available ingredients
     * @param preferences user dietary preferences (can be null for guests)
     * @return the constructed prompt string
     */
    public String buildRecipePrompt(List<String> ingredients, UserPreferencesDto preferences) {
        String ingredientsList = formatIngredients(ingredients);
        String preferencesSection = buildPreferencesSection(preferences);
        
        return String.format(PROMPT_TEMPLATE, ingredientsList, preferencesSection);
    }

    /**
     * Build a recipe generation prompt without preferences (for guests).
     * 
     * @param ingredients list of available ingredients
     * @return the constructed prompt string
     */
    public String buildRecipePrompt(List<String> ingredients) {
        return buildRecipePrompt(ingredients, null);
    }

    /**
     * Build the preferences section of the prompt.
     * 
     * @param preferences user preferences or null
     * @return formatted preferences instructions
     */
    private String buildPreferencesSection(UserPreferencesDto preferences) {
        if (preferences == null) {
            return "User Preferences: None specified. Create a generally appealing recipe.";
        }

        List<String> constraints = new ArrayList<>();
        List<String> preferences_list = new ArrayList<>();

        // Handle allergies - CRITICAL SAFETY CONSTRAINT
        if (preferences.getAllergies() != null && !preferences.getAllergies().isEmpty()) {
            String allergyList = String.join(", ", preferences.getAllergies());
            constraints.add(String.format(
                "CRITICAL SAFETY REQUIREMENT - Absolutely DO NOT include any of these allergens: %s. " +
                "This is for user safety and must be strictly followed.", 
                allergyList
            ));
        }

        // Handle ingredients to avoid (if the field exists)
        // Note: This field may be added in future iterations
        // Currently commented out until DTO is updated
        /*
        if (preferences.getAvoidIngredients() != null && !preferences.getAvoidIngredients().isEmpty()) {
            String avoidList = String.join(", ", preferences.getAvoidIngredients());
            constraints.add(String.format(
                "Please avoid using these ingredients (user preference): %s", 
                avoidList
            ));
        }
        */

        // Handle dietary restrictions
        if (preferences.getIsVegetarian() != null && preferences.getIsVegetarian()) {
            constraints.add("The recipe must be vegetarian (no meat, poultry, or fish)");
        }

        if (preferences.getDiet() != null && !preferences.getDiet().equals("NONE")) {
            String dietConstraint = getDietConstraint(preferences.getDiet());
            if (dietConstraint != null) {
                constraints.add(dietConstraint);
            }
        }

        // Handle preferences (non-critical)
        if (preferences.getPreferredCuisines() != null && !preferences.getPreferredCuisines().isEmpty()) {
            String cuisineList = String.join(", ", preferences.getPreferredCuisines());
            preferences_list.add(String.format(
                "Preferred cuisine styles: %s (incorporate if possible)", 
                cuisineList
            ));
        }

        if (preferences.getSpiceLevel() != null) {
            String spicePreference = getSpiceLevelDescription(preferences.getSpiceLevel());
            if (spicePreference != null) {
                preferences_list.add(spicePreference);
            }
        }

        if (preferences.getMaxCookingTime() != null) {
            preferences_list.add(String.format(
                "Try to keep total cooking time under %d minutes", 
                preferences.getMaxCookingTime()
            ));
        }

        if (preferences.getServingSize() != null) {
            preferences_list.add(String.format(
                "Recipe should serve approximately %d people", 
                preferences.getServingSize()
            ));
        }

        if (preferences.getUnitSystem() != null) {
            String unitSystem = preferences.getUnitSystem().equals("IMPERIAL") 
                ? "imperial (cups, tablespoons, ounces, Fahrenheit)" 
                : "metric (grams, milliliters, Celsius)";
            preferences_list.add(String.format(
                "Use %s units for measurements", 
                unitSystem
            ));
        }

        // Build the final preferences section
        StringBuilder section = new StringBuilder("User Preferences and Requirements:\n");
        
        if (!constraints.isEmpty()) {
            section.append("\nMandatory Constraints:\n");
            for (String constraint : constraints) {
                section.append("- ").append(constraint).append("\n");
            }
        }
        
        if (!preferences_list.isEmpty()) {
            section.append("\nPreferences (accommodate when possible):\n");
            for (String pref : preferences_list) {
                section.append("- ").append(pref).append("\n");
            }
        }

        // Add locale-based instructions if specified
        if (preferences.getLocale() != null) {
            section.append("\n").append(getLocaleInstructions(preferences.getLocale()));
        }

        return section.toString();
    }

    /**
     * Format the ingredients list for the prompt.
     * 
     * @param ingredients list of ingredients
     * @return formatted string
     */
    private String formatIngredients(List<String> ingredients) {
        if (ingredients == null || ingredients.isEmpty()) {
            return "No specific ingredients provided";
        }
        return ingredients.stream()
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.joining(", "));
    }

    /**
     * Get diet-specific constraint description.
     * 
     * @param diet the diet type
     * @return constraint description
     */
    private String getDietConstraint(String diet) {
        return switch (diet) {
            case "LOW_CARB" -> "The recipe should be low in carbohydrates (minimize rice, pasta, bread, sugars)";
            case "HIGH_PROTEIN" -> "The recipe should be high in protein content";
            case "KETO" -> "The recipe must be ketogenic (very low carb, high fat, moderate protein)";
            case "PALEO" -> "The recipe must follow paleo diet principles (no grains, legumes, dairy, or processed foods)";
            case "MEDITERRANEAN" -> "The recipe should follow Mediterranean diet principles (emphasis on vegetables, fruits, whole grains, fish, olive oil)";
            case "VEGAN" -> "The recipe must be strictly vegan (no animal products of any kind)";
            default -> null;
        };
    }

    /**
     * Get spice level description.
     * 
     * @param spiceLevel the spice level
     * @return spice preference description
     */
    private String getSpiceLevelDescription(String spiceLevel) {
        return switch (spiceLevel) {
            case "NONE" -> "No spicy ingredients at all";
            case "MILD" -> "Very mild spice level only";
            case "MEDIUM" -> "Medium spice level is preferred";
            case "HOT" -> "Hot and spicy dishes are welcome";
            case "EXTRA_HOT" -> "Extra hot and very spicy dishes are preferred";
            default -> null;
        };
    }

    /**
     * Get locale-specific instructions.
     * 
     * @param locale the user's locale
     * @return locale-specific instructions
     */
    private String getLocaleInstructions(String locale) {
        if (locale.startsWith("zh")) {
            return "Consider incorporating Chinese cooking techniques and flavor profiles when appropriate.";
        } else if (locale.startsWith("ja")) {
            return "Consider incorporating Japanese cooking techniques and presentation styles when appropriate.";
        } else if (locale.startsWith("ko")) {
            return "Consider incorporating Korean cooking techniques and flavor profiles when appropriate.";
        } else if (locale.startsWith("es")) {
            return "Consider incorporating Spanish or Latin American cooking styles when appropriate.";
        } else if (locale.startsWith("it")) {
            return "Consider incorporating Italian cooking techniques and traditions when appropriate.";
        } else if (locale.startsWith("fr")) {
            return "Consider incorporating French cooking techniques and presentation when appropriate.";
        }
        return "";
    }

    /**
     * Build a simplified prompt for quick recipe suggestions.
     * 
     * @param ingredients available ingredients
     * @param preferences user preferences
     * @param maxRecipes maximum number of recipes to suggest
     * @return the constructed prompt
     */
    public String buildQuickSuggestionPrompt(List<String> ingredients, UserPreferencesDto preferences, int maxRecipes) {
        String ingredientsList = formatIngredients(ingredients);
        String preferencesSection = buildPreferencesSection(preferences);
        
        return String.format("""
            Based on these ingredients: %s
            
            %s
            
            Please suggest %d quick recipe ideas with brief descriptions (2-3 sentences each).
            Focus on variety and creativity while respecting all dietary constraints.
            """, ingredientsList, preferencesSection, maxRecipes);
    }
}