package com.foodmagic.cache;

import com.foodmagic.recipe.dto.RecipeResponseDto;
import com.foodmagic.user.dto.UserPreferencesDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Service for managing recipe cache with user preferences integration.
 * Handles cache key generation, eviction, and retrieval strategies.
 * 
 * <AUTHOR> (Senior QA)
 * @since 1.0
 */
@Service
public class RecipeCacheService {

    private static final Logger logger = LoggerFactory.getLogger(RecipeCacheService.class);
    private static final String RECIPE_CACHE_NAME = "recipes";
    private static final int CACHE_TTL_HOURS = 24;

    /**
     * Generate a unique cache key based on ingredients and user preferences.
     * 
     * @param ingredients list of ingredients
     * @param preferences user preferences (can be null)
     * @param locale user locale for localization
     * @param unitSystem measurement unit system
     * @return a unique cache key
     */
    public String generateCacheKey(
            List<String> ingredients,
            UserPreferencesDto preferences,
            String locale,
            String unitSystem) {
        
        StringBuilder keyBuilder = new StringBuilder("recipe:");
        
        // Sort ingredients for consistent key generation
        String sortedIngredients = ingredients.stream()
                .map(String::toLowerCase)
                .sorted()
                .collect(Collectors.joining(","));
        
        keyBuilder.append(hashString(sortedIngredients));
        
        // Add preferences to key
        if (preferences != null) {
            keyBuilder.append(":prefs:");
            
            // Add diet type
            if (preferences.getDiet() != null) {
                keyBuilder.append("d=").append(preferences.getDiet());
            }
            
            // Add vegetarian flag
            if (preferences.getIsVegetarian() != null && preferences.getIsVegetarian()) {
                keyBuilder.append(",veg");
            }
            
            // Add allergies (sorted for consistency)
            if (preferences.getAllergies() != null && !preferences.getAllergies().isEmpty()) {
                String allergies = preferences.getAllergies().stream()
                        .sorted()
                        .collect(Collectors.joining("-"));
                keyBuilder.append(",alg=").append(hashString(allergies));
            }
            
            // Add cuisines (sorted for consistency)
            if (preferences.getPreferredCuisines() != null && !preferences.getPreferredCuisines().isEmpty()) {
                String cuisines = preferences.getPreferredCuisines().stream()
                        .sorted()
                        .collect(Collectors.joining("-"));
                keyBuilder.append(",cui=").append(hashString(cuisines));
            }
            
            // Add spice level
            if (preferences.getSpiceLevel() != null) {
                keyBuilder.append(",sp=").append(preferences.getSpiceLevel());
            }
            
            // Add serving size
            if (preferences.getServingSize() != null) {
                keyBuilder.append(",srv=").append(preferences.getServingSize());
            }
            
            // Add avoided ingredients (when field is added)
            // Currently commented out until DTO is updated
            /*
            if (preferences.getAvoidIngredients() != null && !preferences.getAvoidIngredients().isEmpty()) {
                String avoided = preferences.getAvoidIngredients().stream()
                        .sorted()
                        .collect(Collectors.joining("-"));
                keyBuilder.append(",avoid=").append(hashString(avoided));
            }
            */
            
            // Add cooking time preference
            if (preferences.getMaxCookingTime() != null) {
                keyBuilder.append(",time=").append(preferences.getMaxCookingTime());
            }
        }
        
        // Add locale and unit system
        keyBuilder.append(":locale=").append(locale != null ? locale : "en-US");
        keyBuilder.append(":units=").append(unitSystem != null ? unitSystem : "METRIC");
        
        String cacheKey = keyBuilder.toString();
        logger.debug("Generated cache key: {}", cacheKey);
        
        return cacheKey;
    }

    /**
     * Cache a recipe with the given key.
     * 
     * @param cacheKey the cache key
     * @param recipe the recipe to cache
     * @return the cached recipe
     */
    @Cacheable(value = RECIPE_CACHE_NAME, key = "#cacheKey")
    public RecipeResponseDto cacheRecipe(String cacheKey, RecipeResponseDto recipe) {
        logger.debug("Caching recipe with key: {}", cacheKey);
        return recipe;
    }

    /**
     * Evict all cached recipes for a specific user when preferences change.
     * Note: In a real implementation, we'd need to track user-specific cache keys
     * for more targeted eviction.
     * 
     * @param userId the user ID whose preferences changed
     */
    @CacheEvict(value = RECIPE_CACHE_NAME, allEntries = true)
    public void evictUserRecipeCache(String userId) {
        logger.info("Evicting all recipe cache entries due to preference change for user: {}", userId);
        // In production, implement more targeted eviction using a user-key mapping
    }

    /**
     * Evict a specific recipe from cache.
     * 
     * @param cacheKey the cache key to evict
     */
    @CacheEvict(value = RECIPE_CACHE_NAME, key = "#cacheKey")
    public void evictRecipe(String cacheKey) {
        logger.debug("Evicting recipe with key: {}", cacheKey);
    }

    /**
     * Clear all recipe cache entries.
     */
    @CacheEvict(value = RECIPE_CACHE_NAME, allEntries = true)
    public void clearAllCache() {
        logger.info("Clearing all recipe cache entries");
    }

    /**
     * Hash a string to create a shorter, consistent key component.
     * 
     * @param input the string to hash
     * @return a hashed string (first 8 chars of base64 encoded SHA-256)
     */
    private String hashString(String input) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(input.getBytes(StandardCharsets.UTF_8));
            String encoded = Base64.getUrlEncoder().encodeToString(hash);
            // Return first 8 characters for brevity
            return encoded.substring(0, 8);
        } catch (NoSuchAlgorithmException e) {
            logger.error("Failed to hash string", e);
            // Fallback to simple hash code
            return String.valueOf(input.hashCode());
        }
    }

    /**
     * Validate if a cache key is still valid based on TTL.
     * This is a placeholder for actual TTL validation logic.
     * 
     * @param cacheKey the cache key to validate
     * @return true if cache is still valid
     */
    public boolean isCacheValid(String cacheKey) {
        // In a real implementation, check timestamp embedded in cache entry
        // For now, rely on Spring Cache's TTL configuration
        return true;
    }
}