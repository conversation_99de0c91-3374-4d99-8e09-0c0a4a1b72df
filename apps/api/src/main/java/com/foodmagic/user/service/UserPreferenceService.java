package com.foodmagic.user.service;

import com.foodmagic.cache.RecipeCacheService;
import com.foodmagic.user.constants.DietType;
import com.foodmagic.user.constants.SpiceLevel;
import com.foodmagic.user.dto.UserPreferencesDto;
import com.foodmagic.user.entity.UserPreference;
import com.foodmagic.user.mapper.UserPreferenceMapper;
import com.foodmagic.user.repository.UserPreferenceRepository;
import com.foodmagic.user.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;
import java.util.UUID;

/**
 * Service for managing user preferences.
 * Handles CRUD operations and business logic for user dietary preferences.
 * 
 * <AUTHOR> (Dev Agent)
 * @since 1.0
 */
@Service
@Transactional
public class UserPreferenceService {

    private static final Logger logger = LoggerFactory.getLogger(UserPreferenceService.class);

    private final UserPreferenceRepository userPreferenceRepository;
    private final UserRepository userRepository;
    private final UserPreferenceMapper mapper;
    private final RecipeCacheService recipeCacheService;

    public UserPreferenceService(
            UserPreferenceRepository userPreferenceRepository,
            UserRepository userRepository,
            UserPreferenceMapper mapper,
            RecipeCacheService recipeCacheService) {
        this.userPreferenceRepository = userPreferenceRepository;
        this.userRepository = userRepository;
        this.mapper = mapper;
        this.recipeCacheService = recipeCacheService;
    }

    /**
     * Get user preferences by user ID.
     * Returns default preferences if none exist.
     * 
     * @param userId the user ID
     * @return UserPreferencesDto containing user preferences or defaults
     */
    @Transactional(readOnly = true)
    public UserPreferencesDto getUserPreferences(UUID userId) {
        logger.debug("Fetching preferences for user: {}", userId);
        
        return userPreferenceRepository.findByUserId(userId)
                .map(mapper::toDto)
                .orElseGet(() -> {
                    logger.debug("No preferences found for user: {}, returning defaults", userId);
                    return mapper.getDefaultPreferences();
                });
    }

    /**
     * Update or create user preferences.
     * Performs upsert operation - creates if not exists, updates if exists.
     * 
     * @param userId the user ID
     * @param preferencesDto the preferences to save
     * @return the saved preferences
     * @throws IllegalArgumentException if user does not exist
     */
    public UserPreferencesDto updateUserPreferences(UUID userId, UserPreferencesDto preferencesDto) {
        logger.debug("Updating preferences for user: {}", userId);

        // Validate user exists
        if (!userRepository.existsById(userId)) {
            logger.error("User not found: {}", userId);
            throw new IllegalArgumentException("User not found with ID: " + userId);
        }

        // Validate preferences
        validatePreferences(preferencesDto);

        // Find existing or create new
        UserPreference preference = userPreferenceRepository.findByUserId(userId)
                .orElseGet(() -> {
                    UserPreference newPref = new UserPreference();
                    newPref.setUserId(userId);
                    return newPref;
                });

        // Update preferences
        Map<String, Object> preferencesMap = mapper.toPreferencesMap(preferencesDto);
        preference.setPreferences(preferencesMap);

        // Save and return
        UserPreference saved = userPreferenceRepository.save(preference);
        logger.info("Successfully updated preferences for user: {}", userId);
        
        // Evict recipe cache when preferences change
        recipeCacheService.evictUserRecipeCache(userId.toString());
        logger.debug("Evicted recipe cache for user: {} after preference update", userId);
        
        return mapper.toDto(saved);
    }

    /**
     * Delete user preferences.
     * 
     * @param userId the user ID
     * @return true if deleted, false if not found
     */
    public boolean deleteUserPreferences(UUID userId) {
        logger.debug("Deleting preferences for user: {}", userId);
        
        if (userPreferenceRepository.existsByUserId(userId)) {
            userPreferenceRepository.deleteByUserId(userId);
            logger.info("Successfully deleted preferences for user: {}", userId);
            return true;
        }
        
        logger.debug("No preferences found to delete for user: {}", userId);
        return false;
    }

    /**
     * Check if user has preferences set.
     * 
     * @param userId the user ID
     * @return true if preferences exist, false otherwise
     */
    @Transactional(readOnly = true)
    public boolean hasPreferences(UUID userId) {
        return userPreferenceRepository.existsByUserId(userId);
    }

    /**
     * Validate preferences data.
     * 
     * @param preferencesDto the preferences to validate
     * @throws IllegalArgumentException if validation fails
     */
    private void validatePreferences(UserPreferencesDto preferencesDto) {
        if (preferencesDto == null) {
            throw new IllegalArgumentException("Preferences cannot be null");
        }

        // Validate allergies list size
        if (preferencesDto.getAllergies() != null && preferencesDto.getAllergies().size() > 50) {
            throw new IllegalArgumentException("Maximum 50 allergies allowed");
        }

        // Validate preferred cuisines list size
        if (preferencesDto.getPreferredCuisines() != null && preferencesDto.getPreferredCuisines().size() > 20) {
            throw new IllegalArgumentException("Maximum 20 cuisine preferences allowed");
        }

        // Validate avoid ingredients list size (when field is added)
        // Currently commented out until DTO is updated
        /*
        if (preferencesDto.getAvoidIngredients() != null && preferencesDto.getAvoidIngredients().size() > 100) {
            throw new IllegalArgumentException("Maximum 100 ingredients to avoid");
        }
        */

        // Validate diet type using enum
        if (preferencesDto.getDiet() != null && !DietType.isValid(preferencesDto.getDiet())) {
            throw new IllegalArgumentException("Invalid diet type: " + preferencesDto.getDiet());
        }

        // Validate unit system
        if (preferencesDto.getUnitSystem() != null) {
            String unitSystem = preferencesDto.getUnitSystem();
            if (!unitSystem.equals("METRIC") && !unitSystem.equals("IMPERIAL")) {
                throw new IllegalArgumentException("Invalid unit system: " + unitSystem);
            }
        }

        // Validate spice level using enum
        if (preferencesDto.getSpiceLevel() != null && !SpiceLevel.isValid(preferencesDto.getSpiceLevel())) {
            throw new IllegalArgumentException("Invalid spice level: " + preferencesDto.getSpiceLevel());
        }

        // Validate cooking time
        if (preferencesDto.getMaxCookingTime() != null) {
            int cookingTime = preferencesDto.getMaxCookingTime();
            if (cookingTime < 0 || cookingTime > 480) {
                throw new IllegalArgumentException("Cooking time must be between 0 and 480 minutes");
            }
        }

        // Validate serving size
        if (preferencesDto.getServingSize() != null) {
            int servingSize = preferencesDto.getServingSize();
            if (servingSize < 1 || servingSize > 20) {
                throw new IllegalArgumentException("Serving size must be between 1 and 20");
            }
        }
    }

    // Validation methods moved to enum classes for better maintainability
}