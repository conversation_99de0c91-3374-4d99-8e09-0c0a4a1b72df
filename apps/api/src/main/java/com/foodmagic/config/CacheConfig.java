package com.foodmagic.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCache;
import org.springframework.cache.support.SimpleCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.Arrays;

/**
 * Cache configuration for recipe caching with Redis.
 * Provides different configurations for production (Redis) and testing (in-memory).
 * 
 * <AUTHOR> (Senior QA)
 * @since 1.0
 */
@Configuration
@EnableCaching
public class CacheConfig {

    /**
     * Production cache manager using Redis.
     * Configures cache TTL, serialization, and eviction policies.
     * 
     * @param connectionFactory Redis connection factory
     * @return configured Redis cache manager
     */
    @Bean
    @Profile({"prod", "dev", "staging"})
    public CacheManager redisCacheManager(RedisConnectionFactory connectionFactory) {
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofHours(24)) // 24-hour TTL as per requirements
                .serializeKeysWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new GenericJackson2JsonRedisSerializer()))
                .disableCachingNullValues(); // Don't cache null values

        return RedisCacheManager.builder(connectionFactory)
                .cacheDefaults(config)
                .withCacheConfiguration("recipes", 
                    config.entryTtl(Duration.ofHours(24))) // Recipe-specific TTL
                .withCacheConfiguration("user-preferences",
                    config.entryTtl(Duration.ofMinutes(30))) // Shorter TTL for preferences
                .transactionAware() // Enable transaction support
                .build();
    }

    /**
     * Test cache manager using in-memory cache.
     * Used for unit and integration tests to avoid Redis dependency.
     * 
     * @return in-memory cache manager
     */
    @Bean
    @Profile({"test", "local"})
    public CacheManager testCacheManager() {
        SimpleCacheManager cacheManager = new SimpleCacheManager();
        cacheManager.setCaches(Arrays.asList(
                new ConcurrentMapCache("recipes"),
                new ConcurrentMapCache("user-preferences")
        ));
        return cacheManager;
    }

    /**
     * Cache key generator for consistent key generation.
     * Can be customized per cache requirement.
     * 
     * @return custom key generator
     */
    @Bean
    public org.springframework.cache.interceptor.KeyGenerator customKeyGenerator() {
        return (target, method, params) -> {
            StringBuilder sb = new StringBuilder();
            sb.append(target.getClass().getSimpleName());
            sb.append(".");
            sb.append(method.getName());
            for (Object param : params) {
                sb.append(".");
                if (param != null) {
                    sb.append(param.toString());
                }
            }
            return sb.toString();
        };
    }
}